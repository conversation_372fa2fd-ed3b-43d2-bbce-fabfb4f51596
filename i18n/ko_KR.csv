"Url Path","URL 경로"
"After Adding a Product Redirect to Firecheckout","Firecheckout으로 제품 리디렉션을 추가 한 후"
"Page Layout","페이지 레이아웃"
"Form Styles","양식 스타일"
"Use field placeholders instead of labels","레이블 대신 필드 자리 표시자를 사용하십시오."
"Show field errors and notices in the Tooltips","툴팁에 필드 오류 및 알림 표시"
"Additional Content","추가 내용"
"Above Firecheckout Form","Firecheckout 양식 위"
"Below Firecheckout Form","Firecheckout 양식 아래"
"Below order summary (Above place order button)","주문 요약 아래 (장소 주문 버튼 위)"
"Below place order button","장소 주문 버튼 아래"
"Intro Popup","소개 팝업"
"Content for Guest Customers","게스트 고객을위한 컨텐츠"
"Content for Registered Customers","등록 고객을위한 컨텐츠"
"Default Method","기본 방법"
"Default Method Code","기본 메소드 코드"
"Use this field if you can't find the method you are looking for in the previous option","이전 옵션에서 찾고있는 방법을 찾을 수없는 경우이 필드를 사용하십시오."
"Hide methods if single method is available only","단일 방법 만 사용할 수있는 경우 방법 숨기기"
"Payment Settings","결제 설정"
"Show Billing Address Title","청구 지 주소 제목 표시"
"This option is not used when Billing Address displayed inside Payment Method","이 방법은 청구서 수신 주소가 결제 수단에 표시 될 때 사용되지 않습니다"
"Horizontal (Label aside of the field)","가로 (필드 옆에 레이블)"
"Basic (Same as horizontal, except label above the field)","기본 (필드 위의 레이블을 제외하고 가로와 동일)"
"Compact (Multiple fields per row)","컴팩트 (행당 여러 필드)"
"1 Column (Multistep Wizard)","1 열 (다단계 마법사)"
"1 Column (Expanded)","1 열 (확장)"
"2 Columns (Wide Payment and Shipping sections)","2 열 (전체 결제 및 배송 섹션)"
"2 Columns (Place Payment and Shipping sections side by side)","2 열 (지불 및 배송 섹션을 나란히 배치)"
"3 Columns","3 열"
"Default Checkout Layout","기본 결제 레이아웃"
"Empty (No Header and Footer)","비우기 (머리글 및 바닥 글 없음)"
"Minimal (Header with store Logo only)","최소 (상점 로고가있는 헤더 만)"
Full,완전한
"Back to Store","매장으로 돌아 가기"
"Continue Shopping","쇼핑을 계속"
"Firecheckout Page Header Container","Firecheckout 페이지 헤더 컨테이너"
"Firecheckout Page Header","Firecheckout 페이지 헤더"
"Please specify a shipping method.","배송 방법을 지정하십시오."
"Please specify a payment method.","결제 수단을 지정하십시오."
Cart,카트
"Use Magento Config (Sales > Checkout > Checkout Options)","Magento 구성 사용 (판매> 결제> 결제 옵션)"
"Payment Page (Above Payment Methods)","결제 페이지 (지불 방법)"
"Shipping Page (Below Shipping Address)","배송 페이지 (배송 주소 아래)"
"Some third-party modules may not work correctly with custom address position. In this case, select 'Use Magento Config' option please.","일부 타사 모듈은 사용자 정의 주소 위치에서 올바르게 작동하지 않을 수 있습니다. 이 경우 'Magento 구성 사용'옵션을 선택하십시오."
"Order Summary Settings","주문 요약 설정"
"Show Order Review (Shipping Information)","주문 검토 표시 (배송 정보)"
"Useful for EU countries","EU 국가에 유용"
"Leave empty to add review without the title","제목없이 리뷰를 추가하려면 비워 두십시오."
"Custom CSS and JS","커스텀 CSS와 JS"
"Use less syntax and variables to change styles!<br/>See the list of available variables: <a href=""http://docs.swissuplabs.com/m2/extensions/firecheckout/customization/less-variables/"" title=""Firecheckout Docs"">http://docs.swissuplabs.com/m2/extensions/firecheckout/customization/less-variables/</a><br/>You must run `setup:static-content:deploy` command to see the changes.","더 적은 구문과 변수를 사용하여 스타일을 변경하십시오!<br/>사용 가능한 변수 목록 참조: <a href=""http://docs.swissuplabs.com/m2/extensions/firecheckout/customization/less-variables/"" title=""Firecheckout Docs"">http://docs.swissuplabs.com/m2/extensions/firecheckout/customization/less-variables/</a><br/>`setup:static-content:deploy` 명령을 실행하여 변경합니다."
Performance,공연
"Use jsBuild","jsBuild 사용"
"Combine js files into single jsbuild to reduce number of requests and improve page load time.","js 파일을 단일 jsbuild로 결합하여 요청 수를 줄이고 페이지로드 시간을 개선하십시오."
"jsBuild include paths","jsBuild 포함 경로"
