<?php
/**
 * Copyright ©  All rights reserved.
 * See COPYING.txt for license details.
 */
declare(strict_types=1);

namespace CopeX\HyvaTheme\Rewrite\Hyva\Theme\ViewModel;

use Magento\Store\Model\Store as StoreModel;

class StoreSwitcher extends \Hyva\Theme\ViewModel\StoreSwitcher
{

    /**
     * Get stores.
     *
     * @return StoreModel[]
     */
    public function getStores()
    {
        if (!$this->stores) {
            $websites = $this->storeManager->getWebsites();
            $stores = [];
            foreach($websites as $website){
                foreach($website->getStores() as $store){
                    /* @var $store StoreModel */
                    if (!$store->isActive()) {
                        continue;
                    }
                    $stores [] = $store;
                }
            }
            $this->stores = $stores;
        }
        return $this->stores;
    }
}
