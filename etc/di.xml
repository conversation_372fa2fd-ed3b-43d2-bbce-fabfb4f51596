<?xml version="1.0"?>
<config xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="urn:magento:framework:ObjectManager/etc/config.xsd">
    <type name="CopeX\VatValidator\Model\Validator">
        <arguments>
            <argument name="validators" xsi:type="array">
                <item name="finanzonline" xsi:type="object">CopeX\VatValidatorAustria\Model\Validator\Implementation</item>
            </argument>
        </arguments>
    </type>
</config>
