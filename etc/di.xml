<?xml version="1.0"?>
<config xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="urn:magento:framework:ObjectManager/etc/config.xsd">
    <!-- Checkout -->
    <type name="Magento\Checkout\Model\ShippingInformationManagement">
        <plugin name="copex-taxvat-validate-shipping"
                type="CopeX\VatValidationFrontend\Plugin\Model\ShippingInformationManagement" sortOrder="1"/>
    </type>
    <type name="Magento\Quote\Model\BillingAddressManagement">
        <plugin name="copex-taxvat-validate-billing"
                type="CopeX\VatValidationFrontend\Plugin\Model\BillingAddressManagement" sortOrder="1"/>
    </type>
    <type name="Magento\Checkout\Block\Checkout\LayoutProcessor">
        <plugin name="copex-taxvat-validation-info"
                type="CopeX\VatValidationFrontend\Plugin\Block\Checkout\LayoutProcessor" sortOrder="1"/>
    </type>

    <!-- Copy vat_id to address object -->
    <type name="Magento\Tax\Model\Sales\Total\Quote\CommonTaxCollector">
        <plugin name="copex-taxvat-copy-vat-to-address" type="CopeX\VatValidationFrontend\Plugin\Model\TaxSalesTotalQuoteCommonTaxCollector" />
    </type>

    <!-- Multishipping checkout -->
    <type name="Magento\Multishipping\Model\Checkout\Type\Multishipping\State">
        <plugin name="copex-taxvat" type="CopeX\VatValidationFrontend\Plugin\Model\MultishippingState" />
    </type>
    <type name="Magento\Multishipping\Controller\Checkout\CheckItems">
        <plugin name="copex-taxvat-validate-shipping" type="CopeX\VatValidationFrontend\Plugin\Action\MultishippingCheckItems" />
    </type>
    <type name="Magento\Multishipping\Controller\Checkout\Address\SetBilling">
        <plugin name="copex-taxvat-validate-billing" type="CopeX\VatValidationFrontend\Plugin\Action\MultishippingSetBilling" />
    </type>
</config>
