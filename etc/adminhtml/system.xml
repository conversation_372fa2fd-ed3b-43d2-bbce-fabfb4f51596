<?xml version="1.0"?>
<config xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="urn:magento:framework:Config/etc/system_file.xsd">
    <system>
        <section id="copex_vat_validator">
            <group id="vat_validation_frontend" translate="label" sortOrder="10" showInDefault="1" showInWebsite="1" showInStore="1">
                <label>Frontend Validation</label>
                <depends>
                    <field id="*/general/enable">1</field>
                </depends>
                <!-- Field to enable/disable warnings -->
                <field id="enable" translate="label" type="select" sortOrder="10" showInDefault="1" showInWebsite="1" showInStore="1">
                    <label>Show In Checkout</label>
                    <source_model>Magento\Config\Model\Config\Source\Yesno</source_model>
                </field>
                <field id="enable_throw_error" translate="label" type="select" sortOrder="20" showInDefault="1" showInWebsite="1" showInStore="1">
                    <label>Force Error</label>
                    <source_model>Magento\Config\Model\Config\Source\Yesno</source_model>
                    <comment>If the VAT is invalid, the customer can't purchase.</comment>
                    <depends>
                        <field id="*/general/enable">1</field>
                        <field id="*/*/enable">1</field>
                    </depends>
                </field>
                <field id="checkout_tooltip" translate="label" type="textarea" sortOrder="30" showInDefault="1" showInWebsite="1" showInStore="1">
                    <label>Checkout Tooltip Message</label>
                    <comment>This is displayed in checkout. E.g. information that a qualified validation will take place.</comment>
                    <depends>
                        <field id="*/general/enable">1</field>
                        <field id="*/*/enable">1</field>
                    </depends>
                </field>
                <field id="enable_registration" translate="label" type="select" sortOrder="100" showInDefault="1" showInWebsite="1" showInStore="1">
                    <label>Show In Customer Registration</label>
                    <source_model>Magento\Config\Model\Config\Source\Yesno</source_model>
                    <depends>
                        <field id="*/general/enable">1</field>
                    </depends>
                </field>
                <field id="enable_address_edit" translate="label" type="select" sortOrder="200" showInDefault="1" showInWebsite="1" showInStore="1">
                    <label>Show In Customer Address Edit</label>
                    <source_model>Magento\Config\Model\Config\Source\Yesno</source_model>
                    <depends>
                        <field id="*/general/enable">1</field>
                    </depends>
                </field>
                <field id="qualified" translate="label" type="select" sortOrder="300" showInDefault="1" showInWebsite="1" showInStore="1">
                    <label>Qualified Validation in Frontend</label>
                    <source_model>Magento\Config\Model\Config\Source\Yesno</source_model>
                    <depends>
                        <field id="*/general/enable">1</field>
                    </depends>
                </field>
                <field id="delay" translate="label comment" type="text" sortOrder="310" showInDefault="1" showInWebsite="1" showInStore="1">
                    <label>Delay</label>
                    <comment>Delay in milliseconds after input. Default=3000</comment>
                    <depends>
                        <field id="*/general/enable">1</field>
                    </depends>
                </field>
            </group>
        </section>
    </system>
</config>
