<?xml version="1.0" ?>
<config xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:noNamespaceSchemaLocation="urn:magento:module:Magento_Config:etc/system_file.xsd">
    <system>
        <section id="copex_plc" translate="label comment" type="text" sortOrder="800" showInDefault="1"
                 showInWebsite="1" showInStore="1">
            <label>CopeX Post PLC Settings</label>
            <tab>sales</tab>
            <resource>CopeX_Plc::config</resource>
            <group id="client_settings" translate="label comment" type="text" sortOrder="10" showInDefault="1" showInWebsite="1"
                   showInStore="1">
                <label>General</label>
                <field id="version" translate="label" type="text" sortOrder="0" showInDefault="1" showInWebsite="1" showInStore="1">
                    <label>Module Version</label>
                    <frontend_model>CopeX\Plc\Block\Adminhtml\Renderer\Config\Version</frontend_model>
                </field>
                <field id="enabled" translate="label comment" type="select" sortOrder="10" showInDefault="1"
                       showInWebsite="1" showInStore="1">
                    <label>Enabled</label>
                    <source_model>Magento\Config\Model\Config\Source\Yesno</source_model>
                    <validate>required-entry</validate>
                </field>
                <field id="client_id" translate="label comment" type="text" sortOrder="20" showInDefault="1"
                       showInWebsite="1" showInStore="1">
                    <label>Client ID</label>
                    <validate>required-entry</validate>
                    <depends>
                        <field id="copex_plc/client_settings/enabled">1</field>
                    </depends>
                </field>
                <field id="org_unit_id" translate="label comment" type="text" sortOrder="30" showInDefault="1"
                       showInWebsite="1" showInStore="1">
                    <label>Organisation Unit ID</label>
                    <validate>required-entry</validate>
                    <depends>
                        <field id="copex_plc/client_settings/enabled">1</field>
                    </depends>
                </field>
                <field id="org_unit_guid" translate="label comment" type="text" sortOrder="40" showInDefault="1"
                       showInWebsite="1" showInStore="1">
                    <label>Organisation Unit GUID</label>
                    <validate>required-entry</validate>
                    <depends>
                        <field id="copex_plc/client_settings/enabled">1</field>
                    </depends>
                </field>
                <field id="production" translate="label comment" type="select" sortOrder="50" showInDefault="1"
                       showInWebsite="1" showInStore="1">
                    <label>Production Mode?</label>
                    <source_model>Magento\Config\Model\Config\Source\Yesno</source_model>
                    <validate>required-entry</validate>
                    <depends>
                        <field id="copex_plc/client_settings/enabled">1</field>
                    </depends>
                </field>
                <field id="d_width" translate="label comment" type="text" sortOrder="60" showInDefault="1"
                       showInWebsite="1" showInStore="1">
                    <label>Default Width</label>
                    <comment>Width in cm. Only use if you know the implications.</comment>
                    <depends>
                        <field id="copex_plc/client_settings/enabled">1</field>
                    </depends>
                </field>
                <field id="d_height" translate="label comment" type="text" sortOrder="70" showInDefault="1"
                       showInWebsite="1" showInStore="1">
                    <label>Default Height</label>
                    <comment>Height in cm. Only use if you know the implications.</comment>
                    <depends>
                        <field id="copex_plc/client_settings/enabled">1</field>
                    </depends>
                </field>
                <field id="d_length" translate="label comment" type="text" sortOrder="80" showInDefault="1"
                       showInWebsite="1" showInStore="1">
                    <label>Default Length</label>
                    <comment>Length in cm. Only use if you know the implications.</comment>
                    <depends>
                        <field id="copex_plc/client_settings/enabled">1</field>
                    </depends>
                </field>
                <field id="d_weight" translate="label comment" type="text" sortOrder="90" showInDefault="1"
                       showInWebsite="1" showInStore="1">
                    <label>Default Weight</label>
                    <comment>Default Weight in kg</comment>
                    <depends>
                        <field id="copex_plc/client_settings/enabled">1</field>
                    </depends>
                </field>
            </group>
            <group id="store" translate="label comment" type="text" sortOrder="20" showInDefault="1" showInWebsite="1"
                   showInStore="1">
                <label>Store Information</label>
                <field id="use_from_store" translate="label comment" type="select" sortOrder="3" showInDefault="1"
                       showInWebsite="1" showInStore="1">
                    <label>Use information from store</label>
                    <comment>Information will be used from Shops > Configuration > General > General > Store Information</comment>
                    <source_model>Magento\Config\Model\Config\Source\Yesno</source_model>
                    <validate>required-entry</validate>
                </field>
                <field id="name" translate="label comment" type="text" sortOrder="3" showInDefault="1"
                       showInWebsite="1" showInStore="1">
                    <label>Name</label>
                    <validate>required-entry</validate>
                    <depends>
                        <field id="copex_plc/store/use_from_store">0</field>
                    </depends>
                </field>
                <field id="street_line1" translate="label comment" type="text" sortOrder="3" showInDefault="1"
                       showInWebsite="1" showInStore="1">
                    <label>Street 1</label>
                    <validate>required-entry</validate>
                    <depends>
                        <field id="copex_plc/store/use_from_store">0</field>
                    </depends>
                </field>
                <field id="street_line2" translate="label comment" type="text" sortOrder="3" showInDefault="1"
                       showInWebsite="1" showInStore="1">
                    <label>Street 2</label>
                    <validate>required-entry</validate>
                    <depends>
                        <field id="copex_plc/store/use_from_store">0</field>
                    </depends>
                </field>
                <field id="city" translate="label comment" type="text" sortOrder="3" showInDefault="1"
                       showInWebsite="1" showInStore="1">
                    <label>City</label>
                    <validate>required-entry</validate>
                    <depends>
                        <field id="copex_plc/store/use_from_store">0</field>
                    </depends>
                </field>
                <field id="country_id" translate="label comment" type="select" sortOrder="3" showInDefault="1"
                       showInWebsite="1" showInStore="1">
                    <label>Country</label>
                    <validate>required-entry</validate>
                    <source_model>Magento\Directory\Model\Config\Source\Country</source_model>
                    <depends>
                        <field id="copex_plc/store/use_from_store">0</field>
                    </depends>
                </field>
                <field id="email" translate="label comment" type="text" sortOrder="3" showInDefault="1"
                       showInWebsite="1" showInStore="1">
                    <label>Email</label>
                    <validate>required-entry</validate>
                    <depends>
                        <field id="copex_plc/store/use_from_store">0</field>
                    </depends>
                </field>
                <field id="postcode" translate="label comment" type="text" sortOrder="3" showInDefault="1"
                       showInWebsite="1" showInStore="1">
                    <label>Postcode</label>
                    <validate>required-entry</validate>
                    <depends>
                        <field id="copex_plc/store/use_from_store">0</field>
                    </depends>
                </field>
                <field id="merchant_vat_number" translate="label comment" type="text" sortOrder="3" showInDefault="1"
                       showInWebsite="1" showInStore="1">
                    <label>VAT ID</label>
                    <depends>
                        <field id="copex_plc/store/use_from_store">0</field>
                    </depends>
                </field>
                <field id="phone" translate="label comment" type="text" sortOrder="3" showInDefault="1"
                       showInWebsite="1" showInStore="1">
                    <label>Phone</label>
                    <depends>
                        <field id="copex_plc/store/use_from_store">0</field>
                    </depends>
                </field>
            </group>
            <group id="printing" translate="label comment" type="text" sortOrder="30" showInDefault="1" showInWebsite="1"
                   showInStore="1">
                <label>Printing</label>
                <field id="layout" translate="label comment" type="select" sortOrder="1" showInDefault="1"
                       showInWebsite="1" showInStore="1">
                    <label>Label Layout</label>
                    <validate>required-entry</validate>
                    <comment>Select how the label should be printed. If "No Label" is selected the label information is sent to PLC but not downloaded.</comment>
                    <source_model>CopeX\Plc\Model\Config\Source\Layout</source_model>
                </field>
                <field id="format" translate="label comment" type="select" sortOrder="2" showInDefault="1"
                       showInWebsite="1" showInStore="1">
                    <label>Label Format</label>
                    <validate>required-entry</validate>
                    <comment>Select format of printing label</comment>
                    <source_model>CopeX\Plc\Model\Config\Source\Format</source_model>
                    <depends>
                        <field id="copex_plc/printing/layout" separator=",">2xA5inA4,A4,A5</field>
                    </depends>
                </field>
                <field id="type" translate="label comment" type="select" sortOrder="3" showInDefault="1"
                       showInWebsite="1" showInStore="1">
                    <label>Label Type</label>
                    <validate>required-entry</validate>
                    <comment>Select type of printing label</comment>
                    <source_model>CopeX\Plc\Model\Config\Source\Type</source_model>
                    <depends>
                        <field id="copex_plc/printing/layout" separator=",">2xA5inA4,A4,A5</field>
                    </depends>
                </field>
                <field id="content_type_zpl" translate="label comment" type="text" sortOrder="4" showInDefault="1"
                       showInWebsite="1" showInStore="1">
                    <label>ZPL Download Mime Type</label>
                    <validate>required-entry</validate>
                    <comment>Choose which content type the file download should have (text/zpl, x-application/zpl, application/epl2, ...)</comment>
                    <depends>
                        <field id="copex_plc/printing/type">zpl2</field>
                    </depends>
                </field>
            </group>
            <group id="shipping" showInDefault="1" showInStore="1" showInWebsite="1" sortOrder="40" translate="label">
                <label>Shipment</label>
                <field id="create_shipment" showInDefault="1" showInStore="1" showInWebsite="1" sortOrder="10"
                       translate="label comment" type="select">
                    <label>Create Shipment automatically</label>
                    <comment>Create shipment and add tracking number on label creation</comment>
                    <source_model>Magento\Config\Model\Config\Source\Yesno</source_model>
                </field>
                <field id="shipment_send" showInDefault="1" showInStore="1" showInWebsite="1" sortOrder="20"
                       translate="label comment" type="select">
                    <label>Send notification to customer</label>
                    <comment>Notify customer about shipping</comment>
                    <source_model>Magento\Config\Model\Config\Source\Yesno</source_model>
                    <depends>
                        <field id="copex_plc/shipping/create_shipment">1</field>
                    </depends>
                </field>
                <field id="shipping_method_favorites" showInDefault="1" showInStore="1" showInWebsite="1" sortOrder="30"
                       translate="label comment" type="multiselect">
                    <label>Shipping Method Favorites</label>
                    <comment>This shipping methods will be displayed first</comment>
                    <source_model>CopeX\Plc\Model\Config\Source\AllServices</source_model>
                </field>
            </group>
            <group id="sales" showInDefault="1" showInStore="1" showInWebsite="1" sortOrder="50" translate="label">
                <label>Sales</label>
                <field id="show_labels_in_admin_order" showInDefault="1" showInStore="1" showInWebsite="1" sortOrder="10"
                       translate="label comment" type="select">
                    <label>Show already created labels in admin order view</label>
                    <source_model>Magento\Config\Model\Config\Source\Yesno</source_model>
                </field>
                <field id="show_form_in_admin_order" showInDefault="1" showInStore="1" showInWebsite="1" sortOrder="20"
                       translate="label comment" type="select">
                    <label>Display Quick Create Labels in Admin Order View</label>
                    <comment>Section where you can create labels for orders easily</comment>
                    <source_model>Magento\Config\Model\Config\Source\Yesno</source_model>
                </field>
                <field id="create_invoice" showInDefault="1" showInStore="1" showInWebsite="1" sortOrder="30"
                       translate="label comment" type="select">
                    <label>Create Invoice automatically</label>
                    <comment>Create invoice on label creation</comment>
                    <source_model>CopeX\Plc\Model\Config\Source\Invoice</source_model>
                </field>
                <field id="automatically_create_label_for_other_carriers" showInDefault="1" showInStore="1" showInWebsite="1" sortOrder="40"
                       translate="label comment" type="select">
                    <label>Create label automatically if order was shipped with other carrier</label>
                    <source_model>Magento\Config\Model\Config\Source\Yesno</source_model>
                </field>
                <field id="automatically_create_label_for_other_carriers_specific" showInDefault="1" showInStore="1" showInWebsite="1" sortOrder="50"
                       translate="label comment" type="multiselect">
                    <label>Carriers to create label automatically</label>
                    <comment>When a shipment is created for one of the selected carriers, a PLC label is created automatically</comment>
                    <source_model>CopeX\Plc\Model\Config\Source\Carriers</source_model>
                    <depends>
                        <field id="automatically_create_label_for_other_carriers">1</field>
                    </depends>
                </field>
                <field id="automatically_shipping_method_domestic" showInDefault="1" showInStore="1" showInWebsite="1" sortOrder="60"
                       translate="label comment" type="select">
                    <label>Default Shipping Method Domestic</label>
                    <source_model>CopeX\Plc\Model\Config\Source\Services</source_model>
                    <depends>
                        <field id="automatically_create_label_for_other_carriers">1</field>
                    </depends>
                </field>
                <field id="automatically_shipping_method_international" showInDefault="1" showInStore="1" showInWebsite="1" sortOrder="70"
                       translate="label comment" type="select">
                    <label>Default Shipping Method International</label>
                    <source_model>CopeX\Plc\Model\Config\Source\Services</source_model>
                    <depends>
                        <field id="automatically_create_label_for_other_carriers">1</field>
                    </depends>
                </field>
            </group>
            <group id="retouring" showInDefault="1" showInStore="1" showInWebsite="1" sortOrder="50" translate="label comment">
                <label>Return Label</label>
                <field id="enable_retouring" showInDefault="1" showInStore="1" showInWebsite="1" sortOrder="10"
                       translate="label comment" type="select">
                    <label>Enable Returns</label>
                    <comment/>
                    <source_model>Magento\Config\Model\Config\Source\Yesno</source_model>
                </field>
                <field id="automatic_retouring_with_label" showInDefault="1" showInStore="1" showInWebsite="1" sortOrder="10"
                       translate="label comment" type="select">
                    <label>Automatically add return label to shipping label</label>
                    <comment>This will print the return label automatically next to the shipping label in a pdf printout</comment>
                    <source_model>Magento\Config\Model\Config\Source\Yesno</source_model>
                    <depends>
                        <field id="enable_retouring">1</field>
                    </depends>
                </field>
                <field id="no_international_retouring" showInDefault="1" showInStore="1" showInWebsite="1" sortOrder="10"
                       translate="label comment" type="select">
                    <label>Don't allow returns for international packages</label>
                    <comment/>
                    <source_model>Magento\Config\Model\Config\Source\Yesno</source_model>
                    <depends>
                        <field id="enable_retouring">1</field>
                        <field id="automatic_retouring_with_label">1</field>
                    </depends>
                </field>
                <field id="enable_retouring_frontend" showInDefault="1" showInStore="1" showInWebsite="1" sortOrder="10"
                       translate="label comment" type="select">
                    <label>Enable Customer Returns</label>
                    <comment>Allow customer in frontend to create return label</comment>
                    <source_model>Magento\Config\Model\Config\Source\Yesno</source_model>
                    <depends>
                        <field id="enable_retouring">1</field>
                    </depends>
                </field>
                <field id="days_customer_retouring" showInDefault="1" showInStore="1" showInWebsite="1" sortOrder="10"
                       translate="label comment" type="text">
                    <label>Maximum age of order</label>
                    <comment>How many days after placing the order, the customer is allowed create a return label. e.g. 30 (-1 forever)</comment>
                    <depends>
                        <field id="enable_retouring">1</field>
                        <field id="enable_retouring_frontend">1</field>
                    </depends>
                </field>
                <field id="enable_retouring_without_label" showInDefault="1" showInStore="1" showInWebsite="1" sortOrder="10"
                       translate="label comment" type="select">
                    <label>Allow returns without prior label</label>
                    <comment/>
                    <source_model>Magento\Config\Model\Config\Source\Yesno</source_model>
                    <depends>
                        <field id="enable_retouring">1</field>
                    </depends>
                </field>
            </group>

            <!-- PostPay Refund Settings Group -->
            <group id="postpay_refund" translate="label comment" type="text" sortOrder="55" showInDefault="1" showInWebsite="1" showInStore="1">
                <label>PostPay Refund Settings</label>
                <comment>Configure PostPay refund API settings for automatic refund processing</comment>
                <depends>
                    <field id="copex_plc/store/use_from_store">0</field>
                </depends>
                <field id="enabled" translate="label comment" type="select" sortOrder="10" showInDefault="1" showInWebsite="1" showInStore="1">
                    <label>Enable PostPay Refunds</label>
                    <source_model>Magento\Config\Model\Config\Source\Yesno</source_model>
                    <comment>Enable automatic PostPay refunds when credit memos are created</comment>
                </field>
                <field id="username" translate="label comment" type="text" sortOrder="20" showInDefault="1" showInWebsite="1" showInStore="1">
                    <label>Username</label>
                    <depends>
                        <field id="enabled">1</field>
                    </depends>
                </field>
                <field id="password" translate="label comment" type="obscure" sortOrder="30" showInDefault="1" showInWebsite="1" showInStore="1">
                    <label>Password</label>
                    <backend_model>Magento\Config\Model\Config\Backend\Encrypted</backend_model>
                    <depends>
                        <field id="enabled">1</field>
                    </depends>
                </field>
            </group>

            <group id="advanced" showInDefault="1" showInStore="1" showInWebsite="1" sortOrder="60" translate="label comment">
                <label>Advanced Options</label>
                <field id="js_download" showInDefault="1" showInStore="1" showInWebsite="1" sortOrder="10"
                       translate="label comment" type="select">
                    <label>Javascript Download</label>
                    <comment/>
                    <source_model>Magento\Config\Model\Config\Source\Yesno</source_model>
                </field>
                <field id="use_icons" showInDefault="1" showInStore="1" showInWebsite="1" sortOrder="10"
                       translate="label comment" type="select">
                    <label>Use Icons instead of Buttons</label>
                    <comment/>
                    <source_model>Magento\Config\Model\Config\Source\Yesno</source_model>
                </field>
            </group>
            <group id="post_standard_settings" translate="label comment" type="text" sortOrder="70" showInDefault="1"
                   showInWebsite="1"
                   showInStore="1">
                <label>Paket Austria</label>
                <field id="active" translate="label comment" type="select" sortOrder="1" showInDefault="1"
                       showInWebsite="1" showInStore="1">
                    <label>Active</label>
                    <source_model>Magento\Config\Model\Config\Source\Yesno</source_model>
                    <validate>required-entry</validate>
                </field>
                <field id="l_2kg" translate="label comment" type="text" sortOrder="2" showInDefault="1"
                       showInWebsite="1" showInStore="1">
                    <label>Price till 2 kg</label>
                    <comment>in €</comment>
                    <validate>validate-number</validate>
                </field>
                <field id="l_4kg" translate="label comment" type="text" sortOrder="3" showInDefault="1"
                       showInWebsite="1" showInStore="1">
                    <label>Price till 4 kg</label>
                    <comment>in €</comment>
                    <validate>validate-number</validate>
                </field>
                <field id="l_10kg" translate="label comment" type="text" sortOrder="4" showInDefault="1"
                       showInWebsite="1" showInStore="1">
                    <label>Price till 10 kg</label>
                    <comment>in €</comment>
                    <validate>validate-number</validate>
                </field>
                <field id="l_20kg" translate="label comment" type="text" sortOrder="5" showInDefault="1"
                       showInWebsite="1" showInStore="1">
                    <label>Price till 20 kg</label>
                    <comment>in €</comment>
                    <validate>validate-number</validate>
                </field>
                <field id="l_31_5kg" translate="label comment" type="text" sortOrder="6" showInDefault="1"
                       showInWebsite="1" showInStore="1">
                    <label>Price till 31,5 kg</label>
                    <comment>in €</comment>
                    <validate>validate-number</validate>
                </field>
            </group>
            <group id="post_premium_settings" translate="label comment" type="text" sortOrder="80" showInDefault="1"
                   showInWebsite="1"
                   showInStore="1">
                <label>Premium Paket Austria B2B</label>
                <field id="active" translate="label comment" type="select" sortOrder="1" showInDefault="1"
                       showInWebsite="1" showInStore="1">
                    <label>Active</label>
                    <source_model>Magento\Config\Model\Config\Source\Yesno</source_model>
                    <validate>required-entry</validate>
                </field>
                <field id="l_2kg" translate="label comment" type="text" sortOrder="2" showInDefault="1"
                       showInWebsite="1" showInStore="1">
                    <label>Price till 2 kg</label>
                    <comment>in €</comment>
                    <validate>validate-number</validate>
                </field>
                <field id="l_4kg" translate="label comment" type="text" sortOrder="3" showInDefault="1"
                       showInWebsite="1" showInStore="1">
                    <label>Price till 4 kg</label>
                    <comment>in €</comment>
                    <validate>validate-number</validate>
                </field>
                <field id="l_10kg" translate="label comment" type="text" sortOrder="4" showInDefault="1"
                       showInWebsite="1" showInStore="1">
                    <label>Price till 10 kg</label>
                    <comment>in €</comment>
                    <validate>validate-number</validate>
                </field>
                <field id="l_20kg" translate="label comment" type="text" sortOrder="5" showInDefault="1"
                       showInWebsite="1" showInStore="1">
                    <label>Price till 20 kg</label>
                    <comment>in €</comment>
                    <validate>validate-number</validate>
                </field>
                <field id="l_31_5kg" translate="label comment" type="text" sortOrder="6" showInDefault="1"
                       showInWebsite="1" showInStore="1">
                    <label>Price till 31,5 kg</label>
                    <comment>in €</comment>
                    <validate>validate-number</validate>
                </field>
            </group>
            <group id="post_premium_select_settings" translate="label comment" type="text" sortOrder="110" showInDefault="1"
                   showInWebsite="1"
                   showInStore="1">
                <label>Paket Premium Select Austria</label>
                <field id="active" translate="label comment" type="select" sortOrder="1" showInDefault="1"
                       showInWebsite="1" showInStore="1">
                    <label>Active</label>
                    <source_model>Magento\Config\Model\Config\Source\Yesno</source_model>
                    <validate>required-entry</validate>
                </field>
                <field id="l_2kg" translate="label comment" type="text" sortOrder="2" showInDefault="1"
                       showInWebsite="1" showInStore="1">
                    <label>Price till 2 kg</label>
                    <comment>in €</comment>
                    <validate>validate-number</validate>
                </field>
                <field id="l_4kg" translate="label comment" type="text" sortOrder="3" showInDefault="1"
                       showInWebsite="1" showInStore="1">
                    <label>Price till 4 kg</label>
                    <comment>in €</comment>
                    <validate>validate-number</validate>
                </field>
                <field id="l_8kg" translate="label comment" type="text" sortOrder="4" showInDefault="1"
                       showInWebsite="1" showInStore="1">
                    <label>Price till 8 kg</label>
                    <comment>in €</comment>
                    <validate>validate-number</validate>
                </field>
                <field id="l_10kg" translate="label comment" type="text" sortOrder="5" showInDefault="1"
                       showInWebsite="1" showInStore="1">
                    <label>Price till 10 kg</label>
                    <comment>in €</comment>
                    <validate>validate-number</validate>
                </field>
                <field id="l_12kg" translate="label comment" type="text" sortOrder="6" showInDefault="1"
                       showInWebsite="1" showInStore="1">
                    <label>Price till 12 kg</label>
                    <comment>in €</comment>
                    <validate>validate-number</validate>
                </field>
                <field id="l_20kg" translate="label comment" type="text" sortOrder="7" showInDefault="1"
                       showInWebsite="1" showInStore="1">
                    <label>Price till 20 kg</label>
                    <comment>in €</comment>
                    <validate>validate-number</validate>
                </field>
                <field id="l_31_5kg" translate="label comment" type="text" sortOrder="8" showInDefault="1"
                       showInWebsite="1" showInStore="1">
                    <label>Price till 31,5 kg</label>
                    <comment>in €</comment>
                    <validate>validate-number</validate>
                </field>
            </group>
            <group id="post_combi_freight_international_settings" translate="label comment" type="text" sortOrder="130" showInDefault="1"
                   showInWebsite="1"
                   showInStore="1">
                <label>Combi-freight International</label>
                <field id="active" translate="label comment" type="select" sortOrder="1" showInDefault="1"
                       showInWebsite="1" showInStore="1">
                    <label>Active</label>
                    <source_model>Magento\Config\Model\Config\Source\Yesno</source_model>
                    <validate>required-entry</validate>
                </field>
                <field id="l_2kg" translate="label comment" type="text" sortOrder="2" showInDefault="1"
                       showInWebsite="1" showInStore="1">
                    <label>Price till 2 kg</label>
                    <comment>in €</comment>
                    <validate>validate-number</validate>
                </field>
                <field id="l_4kg" translate="label comment" type="text" sortOrder="3" showInDefault="1"
                       showInWebsite="1" showInStore="1">
                    <label>Price till 4 kg</label>
                    <comment>in €</comment>
                    <validate>validate-number</validate>
                </field>
                <field id="l_8kg" translate="label comment" type="text" sortOrder="4" showInDefault="1"
                       showInWebsite="1" showInStore="1">
                    <label>Price till 8 kg</label>
                    <comment>in €</comment>
                    <validate>validate-number</validate>
                </field>
                <field id="l_10kg" translate="label comment" type="text" sortOrder="5" showInDefault="1"
                       showInWebsite="1" showInStore="1">
                    <label>Price till 10 kg</label>
                    <comment>in €</comment>
                    <validate>validate-number</validate>
                </field>
                <field id="l_12kg" translate="label comment" type="text" sortOrder="6" showInDefault="1"
                       showInWebsite="1" showInStore="1">
                    <label>Price till 12 kg</label>
                    <comment>in €</comment>
                    <validate>validate-number</validate>
                </field>
                <field id="l_20kg" translate="label comment" type="text" sortOrder="7" showInDefault="1"
                       showInWebsite="1" showInStore="1">
                    <label>Price till 20 kg</label>
                    <comment>in €</comment>
                    <validate>validate-number</validate>
                </field>
                <field id="l_31_5kg" translate="label comment" type="text" sortOrder="8" showInDefault="1"
                       showInWebsite="1" showInStore="1">
                    <label>Price till 31,5 kg</label>
                    <comment>in €</comment>
                    <validate>validate-number</validate>
                </field>
            </group>
            <group id="post_nextday_settings" translate="label comment" type="text" sortOrder="140" showInDefault="1"
                   showInWebsite="1"
                   showInStore="1">
                <label>Nextday</label>
                <field id="active" translate="label comment" type="select" sortOrder="1" showInDefault="1"
                       showInWebsite="1" showInStore="1">
                    <label>Active</label>
                    <source_model>Magento\Config\Model\Config\Source\Yesno</source_model>
                    <validate>required-entry</validate>
                </field>
                <field id="l_2kg" translate="label comment" type="text" sortOrder="2" showInDefault="1"
                       showInWebsite="1" showInStore="1">
                    <label>Price till 2 kg</label>
                    <comment>in €</comment>
                    <validate>validate-number</validate>
                </field>
                <field id="l_4kg" translate="label comment" type="text" sortOrder="3" showInDefault="1"
                       showInWebsite="1" showInStore="1">
                    <label>Price till 4 kg</label>
                    <comment>in €</comment>
                    <validate>validate-number</validate>
                </field>
                <field id="l_8kg" translate="label comment" type="text" sortOrder="4" showInDefault="1"
                       showInWebsite="1" showInStore="1">
                    <label>Price till 8 kg</label>
                    <comment>in €</comment>
                    <validate>validate-number</validate>
                </field>
                <field id="l_10kg" translate="label comment" type="text" sortOrder="5" showInDefault="1"
                       showInWebsite="1" showInStore="1">
                    <label>Price till 10 kg</label>
                    <comment>in €</comment>
                    <validate>validate-number</validate>
                </field>
                <field id="l_12kg" translate="label comment" type="text" sortOrder="6" showInDefault="1"
                       showInWebsite="1" showInStore="1">
                    <label>Price till 12 kg</label>
                    <comment>in €</comment>
                    <validate>validate-number</validate>
                </field>
                <field id="l_20kg" translate="label comment" type="text" sortOrder="7" showInDefault="1"
                       showInWebsite="1" showInStore="1">
                    <label>Price till 20 kg</label>
                    <comment>in €</comment>
                    <validate>validate-number</validate>
                </field>
                <field id="l_31_5kg" translate="label comment" type="text" sortOrder="8" showInDefault="1"
                       showInWebsite="1" showInStore="1">
                    <label>Price till 31,5 kg</label>
                    <comment>in €</comment>
                    <validate>validate-number</validate>
                </field>
            </group>
            <group id="post_combi_freight_settings" translate="label comment" type="text" sortOrder="150" showInDefault="1"
                   showInWebsite="1"
                   showInStore="1">
                <label>Combi-freigh Austria</label>
                <field id="active" translate="label comment" type="select" sortOrder="1" showInDefault="1"
                       showInWebsite="1" showInStore="1">
                    <label>Active</label>
                    <source_model>Magento\Config\Model\Config\Source\Yesno</source_model>
                    <validate>required-entry</validate>
                </field>
                <field id="l_2kg" translate="label comment" type="text" sortOrder="2" showInDefault="1"
                       showInWebsite="1" showInStore="1">
                    <label>Price till 2 kg</label>
                    <comment>in €</comment>
                    <validate>validate-number</validate>
                </field>
                <field id="l_4kg" translate="label comment" type="text" sortOrder="3" showInDefault="1"
                       showInWebsite="1" showInStore="1">
                    <label>Price till 4 kg</label>
                    <comment>in €</comment>
                    <validate>validate-number</validate>
                </field>
                <field id="l_8kg" translate="label comment" type="text" sortOrder="4" showInDefault="1"
                       showInWebsite="1" showInStore="1">
                    <label>Price till 8 kg</label>
                    <comment>in €</comment>
                    <validate>validate-number</validate>
                </field>
                <field id="l_10kg" translate="label comment" type="text" sortOrder="5" showInDefault="1"
                       showInWebsite="1" showInStore="1">
                    <label>Price till 10 kg</label>
                    <comment>in €</comment>
                    <validate>validate-number</validate>
                </field>
                <field id="l_12kg" translate="label comment" type="text" sortOrder="6" showInDefault="1"
                       showInWebsite="1" showInStore="1">
                    <label>Price till 12 kg</label>
                    <comment>in €</comment>
                    <validate>validate-number</validate>
                </field>
                <field id="l_20kg" translate="label comment" type="text" sortOrder="7" showInDefault="1"
                       showInWebsite="1" showInStore="1">
                    <label>Price till 20 kg</label>
                    <comment>in €</comment>
                    <validate>validate-number</validate>
                </field>
                <field id="l_31_5kg" translate="label comment" type="text" sortOrder="8" showInDefault="1"
                       showInWebsite="1" showInStore="1">
                    <label>Price till 31,5 kg</label>
                    <comment>in €</comment>
                    <validate>validate-number</validate>
                </field>
            </group>
            <group id="post_international_settings" translate="label comment" type="text" sortOrder="180" showInDefault="1"
                   showInWebsite="1"
                   showInStore="1">
                <label>Internationales Paket</label>
                <field id="active" translate="label comment" type="select" sortOrder="1" showInDefault="1"
                       showInWebsite="1" showInStore="1">
                    <label>Active</label>
                    <source_model>Magento\Config\Model\Config\Source\Yesno</source_model>
                    <validate>required-entry</validate>
                </field>
                <field id="l_2kg" translate="label comment" type="text" sortOrder="2" showInDefault="1"
                       showInWebsite="1" showInStore="1">
                    <label>Price till 2 kg</label>
                    <comment>in €</comment>
                    <validate>validate-number</validate>
                </field>
                <field id="l_4kg" translate="label comment" type="text" sortOrder="3" showInDefault="1"
                       showInWebsite="1" showInStore="1">
                    <label>Price till 4 kg</label>
                    <comment>in €</comment>
                    <validate>validate-number</validate>
                </field>
                <field id="l_10kg" translate="label comment" type="text" sortOrder="5" showInDefault="1"
                       showInWebsite="1" showInStore="1">
                    <label>Price till 10 kg</label>
                    <comment>in €</comment>
                    <validate>validate-number</validate>
                </field>
                <field id="l_20kg" translate="label comment" type="text" sortOrder="7" showInDefault="1"
                       showInWebsite="1" showInStore="1">
                    <label>Price till 20 kg</label>
                    <comment>in €</comment>
                    <validate>validate-number</validate>
                </field>
                <field id="l_31_5kg" translate="label comment" type="text" sortOrder="8" showInDefault="1"
                       showInWebsite="1" showInStore="1">
                    <label>Price till 31,5 kg</label>
                    <comment>in €</comment>
                    <validate>validate-number</validate>
                </field>
            </group>
            <group id="post_international_premium_settings" translate="label comment" type="text" sortOrder="190" showInDefault="1"
                   showInWebsite="1"
                   showInStore="1">
                <label>Internationales Premium Paket</label>
                <field id="active" translate="label comment" type="select" sortOrder="1" showInDefault="1"
                       showInWebsite="1" showInStore="1">
                    <label>Active</label>
                    <source_model>Magento\Config\Model\Config\Source\Yesno</source_model>
                    <validate>required-entry</validate>
                </field>
                <field id="l_2kg" translate="label comment" type="text" sortOrder="2" showInDefault="1"
                       showInWebsite="1" showInStore="1">
                    <label>Price till 2 kg</label>
                    <comment>in €</comment>
                    <validate>validate-number</validate>
                </field>
                <field id="l_4kg" translate="label comment" type="text" sortOrder="3" showInDefault="1"
                       showInWebsite="1" showInStore="1">
                    <label>Price till 4 kg</label>
                    <comment>in €</comment>
                    <validate>validate-number</validate>
                </field>
                <field id="l_10kg" translate="label comment" type="text" sortOrder="5" showInDefault="1"
                       showInWebsite="1" showInStore="1">
                    <label>Price till 10 kg</label>
                    <comment>in €</comment>
                    <validate>validate-number</validate>
                </field>
                <field id="l_20kg" translate="label comment" type="text" sortOrder="7" showInDefault="1"
                       showInWebsite="1" showInStore="1">
                    <label>Price till 20 kg</label>
                    <comment>in €</comment>
                    <validate>validate-number</validate>
                </field>
                <field id="l_31_5kg" translate="label comment" type="text" sortOrder="8" showInDefault="1"
                       showInWebsite="1" showInStore="1">
                    <label>Price till 31,5 kg</label>
                    <comment>in €</comment>
                    <validate>validate-number</validate>
                </field>
            </group>
        </section>
        <section id="carriers" showInDefault="1" showInStore="1" showInWebsite="1" sortOrder="1000" translate="label comment">
            <group id="austrianpost" showInDefault="1" showInStore="1" showInWebsite="1" sortOrder="10"
                   translate="label comment">
                <label>Österreichische Post AG</label>
                <field id="active" showInDefault="1" showInStore="1" showInWebsite="1" sortOrder="10" translate="label,comment"
                       type="select">
                    <label>Enabled</label>
                    <source_model>Magento\Config\Model\Config\Source\Yesno</source_model>
                    <comment>For settings please go to "CopeX Post PLC Settings"-Section</comment>
                </field>
                <field id="sort_order" showInDefault="1" showInStore="1" showInWebsite="1" sortOrder="40"
                       translate="label comment" type="text">
                    <label>Sort Order</label>
                </field>
                <field id="sallowspecific" showInDefault="1" showInStore="1" showInWebsite="1" sortOrder="60"
                       translate="label comment" type="select">
                    <label>Ship to Applicable Countries</label>
                    <frontend_class>shipping-applicable-country</frontend_class>
                    <source_model>Magento\Shipping\Model\Config\Source\Allspecificcountries</source_model>
                </field>
                <field id="specificcountry" showInDefault="1" showInStore="1" showInWebsite="1" sortOrder="70"
                       translate="label comment" type="multiselect">
                    <label>Ship to Specific Countries</label>
                    <can_be_empty>1</can_be_empty>
                    <source_model>Magento\Directory\Model\Config\Source\Country</source_model>
                </field>
                <field id="specificerrmsg" showInDefault="1" showInStore="1" showInWebsite="1" sortOrder="80"
                       translate="label comment" type="textarea">
                    <label>Displayed Error Message</label>
                </field>
                <field id="showmethod" translate="label comment" type="select" sortOrder="90" showInDefault="1" showInWebsite="1" showInStore="0">
                    <label>Show Method if Not Applicable</label>
                    <source_model>Magento\Config\Model\Config\Source\Yesno</source_model>
                </field>
            </group>
        </section>
    </system>
</config>
