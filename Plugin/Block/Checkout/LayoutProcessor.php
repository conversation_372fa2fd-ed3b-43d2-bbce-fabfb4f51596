<?php

declare(strict_types=1);

namespace CopeX\VatValidationFrontend\Plugin\Block\Checkout;

use CopeX\VatValidationFrontend\Helper\Data;
use Magento\Checkout\Block\Checkout\LayoutProcessor as BaseLayoutProcessor;
use Magento\Framework\App\ProductMetadataInterface;

class LayoutProcessor
{
    /**
     * @var Data
     */
    protected $helper;

    /**
     * @var ProductMetadataInterface
     */
    protected $magentoMetadata;

    /**
     * LayoutProcessor constructor.
     * @param Data $helper
     * @param ProductMetadataInterface $magentoMetadata
     */
    public function __construct(
        Data $helper,
        ProductMetadataInterface $magentoMetadata
    ) {
        $this->helper = $helper;
        $this->magentoMetadata = $magentoMetadata;
    }

    /**
     * @param BaseLayoutProcessor $subject
     * @param $jsLayout
     * @return mixed
     */
    public function afterProcess(BaseLayoutProcessor $subject, $jsLayout)
    {
        if ($this->helper->isEnabled()) {
            if (isset($jsLayout['components']['checkout']['children']['steps']
                      ['children']['shipping-step']['children']['shippingAddress']
                      ['children']['shipping-address-fieldset']['children']['vat_id'])) {
                $this->addVatTooltip(
                    $jsLayout['components']['checkout']['children']['steps']
                    ['children']['shipping-step']['children']['shippingAddress']
                    ['children']['shipping-address-fieldset']['children']['vat_id']
                );
            }
            if (isset($jsLayout['components']['checkout']['children']['steps']
                      ['children']['billing-step']['children']['payment']['children']
                      ['payments-list']['children'])) {
                $this->addVatTooltipToPaymentForms(
                    $jsLayout['components']['checkout']['children']['steps']
                    ['children']['billing-step']['children']['payment']
                    ['children']['payments-list']['children']
                );
            }
            if (isset($jsLayout['components']['checkout']['children']['steps']
                      ['children']['billing-step']['children']['payment']['children']
                      ['afterMethods']['children']['billing-address-form']['children']
                      ['form-fields']['children']['vat_id'])) {
                $this->addVatTooltip(
                    $jsLayout['components']['checkout']['children']
                    ['steps']['children']['billing-step']['children']['payment']
                    ['children']['afterMethods']['children']
                    ['billing-address-form']['children']['form-fields']
                    ['children']['vat_id']
                );
            }
        }
        return $jsLayout;
    }

    /**
     * @param array $paymentForms
     */
    private function addVatTooltipToPaymentForms(array &$paymentForms)
    {
        foreach ($paymentForms as $key => $values) {
            if (strpos($key, '-form') === false) {
                continue;
            }
            if (!isset($paymentForms[$key]['children']['form-fields']['children']['vat_id'])) {
                continue;
            }
            $this->addVatTooltip(
                $paymentForms[$key]['children']['form-fields']['children']['vat_id']
            );
        }
    }

    /**
     * @param array $vatField
     */
    private function addVatTooltip(array &$vatField)
    {
        $tooltip = $this->helper->getCheckoutTooltip();
        if (version_compare($this->magentoMetadata->getVersion(), '2.3.0', '<')) {
            $tooltip .= __('Please do not enter country code. For example, DE123456789 is wrong, while 123456789 is correct.');
        }
        $vatField['config']['tooltip']['description'] = $tooltip;
    }
}
