<?php

namespace CopeX\LowestPriceHistory\Plugin\Pricing;

use CopeX\LowestPriceHistory\Helper\Config;
use CopeX\LowestPriceHistory\Model\Config\Source\DisplayMode;
use CopeX\LowestPriceHistory\Model\Config\Source\DisplayOn;
use Magento\Catalog\Pricing\Price\TierPrice;
use Magento\Framework\Pricing\Render;
use Magento\Framework\Pricing\SaleableInterface;

/**
 * Plugin for adding the price history block to the price blocks on the product pages.
 */
class AddHistory
{
    /**
     * Config
     *
     * @var Config
     */
    protected $config;

    /**
     * AddPriceDetailsPlugin constructor.
     * @param Config $config
     */
    public function __construct(Config $config)
    {
        $this->config = $config;
    }

    /**
     * Adds price details to price block.
     *
     * @param Render $subject
     * @param string $result
     * @param string $priceCode
     * @param SaleableInterface $saleableItem
     *
     * @return string
     * @throws \Magento\Framework\Exception\LocalizedException
     */
    public function afterRender(Render $subject, $result, $priceCode, SaleableInterface $saleableItem)
    {
        if ($this->shouldAddPriceDetails($result, $subject, $priceCode, $saleableItem)) {
            $block = $subject->getLayout()->getBlock('copex.producthistory.price.details');
            if ($block) {
                $block->setSaleableItem($saleableItem);
                $result .= $block->toHtml();
            }
        }

        return $result;
    }

    /**
     * Checks if price details should be added.
     *
     * @param bool $result
     * @param string $priceCode
     * @param SaleableInterface $saleableItem
     *
     * @return bool
     */
    public function shouldAddPriceDetails($result, $subject, $priceCode, SaleableInterface $saleableItem): bool
    {
        if (trim($result) === '') {
            return false;
        }

        $layoutHandles = $subject->getLayout()->getUpdate()->getHandles();
        if(in_array('catalog_category_view', $layoutHandles) && $this->config->getDisplayOn() !== DisplayOn::BOTH){
            return false;
        }

        // price details are shown for each child product, not for the grouped product
        if ($saleableItem->getTypeId() === 'grouped') {
            return false;
        }

        // do not show the price details after the tier prices again - they are already added to the final price
        if ($priceCode === TierPrice::PRICE_CODE) {
            return false;
        }

        return true;
    }
}
