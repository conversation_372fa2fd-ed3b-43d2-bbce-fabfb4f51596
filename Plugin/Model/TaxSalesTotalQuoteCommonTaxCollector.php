<?php

declare(strict_types=1);

namespace CopeX\VatValidationFrontend\Plugin\Model;

use CopeX\VatValidationFrontend\Helper\Data;
use Magento\Quote\Model\Quote\Address;
use Magento\Tax\Model\Sales\Total\Quote\CommonTaxCollector;

class TaxSalesTotalQuoteCommonTaxCollector
{
    /**
     * @var Data
     */
    private $helper;

    /**
     * TaxSalesTotalQuoteCommonTaxCollector constructor.
     * @param Data $helper
     */
    public function __construct(
        Data $helper
    ) {
        $this->helper = $helper;
    }

    /**
     * @param CommonTaxCollector $subject
     * @param $customerAddress
     * @param Address $address
     * @return mixed
     */
    public function afterMapAddress(CommonTaxCollector $subject, $customerAddress, Address $address)
    {
        if ($this->helper->isEnabled()) {
            if (!$customerAddress->getVatId() && $address->getVatId()) {
                $customerAddress->setVatId($address->getVatId());
            }
        }

        return $customerAddress;
    }
}
