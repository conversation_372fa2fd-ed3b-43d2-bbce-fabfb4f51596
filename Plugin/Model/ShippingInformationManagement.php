<?php

declare(strict_types=1);

namespace CopeX\VatValidationFrontend\Plugin\Model;

use CopeX\VatValidationFrontend\Helper\Data;
use Magento\Checkout\Api\Data\ShippingInformationInterface;
use Magento\Checkout\Model\ShippingInformationManagement as BaseShippingInformationManagement;
use Magento\Framework\Message\ManagerInterface;

class ShippingInformationManagement
{
    protected $helper;

    public function __construct(
        Data $helper,
        ManagerInterface $messageManager
    ) {
        $this->helper = $helper;
    }

    public function afterSaveAddressInformation(
        BaseShippingInformationManagement $subject,
                                          $result,
                                          $cartId,
        ShippingInformationInterface $addressInformation
    ) {
        if ($this->helper->isEnabled()) {
            $address = $addressInformation->getShippingAddress();
            $this->helper->showWarningMessage($address);
        }

        return $result;
    }
}
