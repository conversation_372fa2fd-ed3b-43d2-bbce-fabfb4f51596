<?php

declare(strict_types=1);

namespace CopeX\VatValidationFrontend\Plugin\Model;

use CopeX\VatValidationFrontend\Helper\Data;
use Magento\Checkout\Model\Session;
use Magento\Customer\Api\AddressRepositoryInterface;
use Magento\Framework\Exception\ValidatorException;
use Magento\Multishipping\Model\Checkout\Type\Multishipping\State;

class MultishippingState
{
    /**
     * @var Data
     */
    private $helper;

    /**
     * @var Session
     */
    private $checkoutSession;

    /**
     * @var AddressRepositoryInterface
     */
    private $addressRepository;

    /**
     * MultishippingState constructor.
     * @param Data $helper
     * @param Session $checkoutSession
     * @param AddressRepositoryInterface $addressRepository
     */
    public function __construct(
        Data $helper,
        Session $checkoutSession,
        AddressRepositoryInterface $addressRepository
    ) {
        $this->helper = $helper;
        $this->checkoutSession = $checkoutSession;
        $this->addressRepository = $addressRepository;
    }

    /**
     * @param State  $subject
     * @param string $step
     * @return void
     * @throws ValidatorException
     */
    public function beforeSetCompleteStep(State $subject, $step)
    {
        if ($this->helper->isEnabled() && $step === State::STEP_BILLING) {
            $this->validateBillingAddress();
        }
    }

    /**
     * @throws ValidatorException
     * @throws \Magento\Framework\Exception\LocalizedException
     * @throws \Magento\Framework\Exception\NoSuchEntityException
     */
    private function validateBillingAddress()
    {
        if (!$this->helper->canValidateVat()) {
            return;
        }
        $address = $this->checkoutSession->getQuote()->getBillingAddress();
        if ($addressId = $address->getCustomerAddressId()) {
            try {
                $address = $this->addressRepository->getById($addressId);
            } catch (\Exception $e) {
                return;
            }
        }
        if ($this->helper->validateAddress($address)) {
            return;
        }
        throw new ValidatorException(__('Please enter a valid VAT number.'));
    }
}
