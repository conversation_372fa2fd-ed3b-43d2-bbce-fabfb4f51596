<?php

declare(strict_types=1);

namespace CopeX\VatValidationFrontend\Plugin\Model;

use CopeX\VatValidationFrontend\Helper\Data;
use Magento\Framework\Exception\RemoteServiceUnavailableException;
use Magento\Framework\Exception\ValidatorException;
use Magento\Quote\Api\Data\AddressInterface;
use Magento\Quote\Model\BillingAddressManagement as BaseBillingAddressManagement;

class BillingAddressManagement
{
    /**
     * @var Data
     */
    protected $helper;

    /**
     * BillingAddressManagement constructor.
     * @param Data $helper
     */
    public function __construct(
        Data $helper
    ) {
        $this->helper = $helper;
    }

    /**
     * @param BaseBillingAddressManagement $subject
     * @param $result
     * @param $cartId
     * @param AddressInterface $address
     * @param false $useForShipping
     * @return mixed
     * @throws ValidatorException
     */
    public function afterAssign(BaseBillingAddressManagement $subject, $result, $cartId, AddressInterface $address, $useForShipping = false)
    {
        if ($this->helper->isEnabled() && !$this->helper->validateAddress($address)) {
            throw new ValidatorException(__('Please enter a valid VAT number.'));
        }
        return $result;
    }
}
