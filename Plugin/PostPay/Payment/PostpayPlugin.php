<?php

declare(strict_types=1);

namespace CopeX\Plc\Plugin\PostPay\Payment;

use CopeX\Plc\Model\Service\PostPayRefundService;
use CopeX\PostPay\Model\Payment\Postpay;
use Magento\Framework\Registry;
use Magento\Payment\Model\InfoInterface;
use Magento\Sales\Model\Order\Creditmemo;
use Magento\Sales\Model\Order\Payment;
use Psr\Log\LoggerInterface;

/**
 * Plugin for PostPay payment method to handle refunds
 */
class PostpayPlugin
{

    private $trackingExists = false;

    public function __construct(
        private readonly PostPayRefundService $postPayRefundService,
        private readonly LoggerInterface $logger,
        private readonly Registry $registry
    ) {
    }

    /**
     * Plugin for canRefund method
     * Allow refunds for PostPay payments when PostPay refund is enabled
     */
    public function afterCanRefund(Postpay $subject, bool $result): bool
    {
        // If parent method already allows refund, keep it
        if ($result) {
            return $result;
        }

        // Check if PostPay refunds are enabled
        if($this->postPayRefundService->isPostPayRefundEnabled() && $this->trackingExists($subject) ){
            /** @var Creditmemo $creditMemo */
            $creditMemo = $this->registry->registry('current_creditmemo');
            if($creditMemo && $this->postPayRefundService->shouldProcessRefund($creditMemo->getOrder())){
                $creditMemo->getInvoice()->setTransactionId(true);
            }
            return true;
        }
        return false;
    }

    /**
     * Plugin to enable online refunds for PostPay
     */
    public function afterCanRefundPartialPerInvoice(Postpay $subject, bool $result): bool
    {
        return $this->postPayRefundService->isPostPayRefundEnabled() && $this->trackingExists($subject) ? true : $result;
    }

    /**
     * Plugin to mark PostPay as supporting online refunds
     */
    public function afterIsGateway(Postpay $subject, bool $result): bool
    {
        return $this->postPayRefundService->isPostPayRefundEnabled() && $this->trackingExists($subject) ? true : $result;
    }

    private function trackingExists(Postpay $postpay){
        $paymentInfo = $postpay->getInfoInstance();
        if ($paymentInfo instanceof Payment) {
            $order = $paymentInfo->getOrder();
            $this->trackingExists = $this->postPayRefundService->trackingExists($order);
        }
        return $this->trackingExists;
    }

    /**
     * Plugin for refund method
     * Process PostPay refund via API when refund is requested
     */
    public function afterRefund(
        Postpay $subject,
        Postpay $result,
        InfoInterface $payment,
        float $amount
    ): Postpay {
        try {
            /** @var Payment $payment */
            $order = $payment->getOrder();
            
            // Find the credit memo for this refund
            $creditMemo = $payment->getCreditmemo();
            
            if ($creditMemo) {
                $this->postPayRefundService->processRefund($creditMemo);
            } else {
                $this->logger->warning('Could not find credit memo for PostPay refund', [
                    'order_id' => $order->getIncrementId(),
                    'payment_id' => $payment->getId(),
                    'refund_amount' => $amount
                ]);
            }
        } catch (\Exception $e) {
            $this->logger->error('PostPay refund failed', [
                'payment_id' => $payment->getId(),
                'amount' => $amount,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            throw $e;
        }

        return $result;
    }
}
