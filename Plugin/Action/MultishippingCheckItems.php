<?php

declare(strict_types=1);

namespace CopeX\VatValidationFrontend\Plugin\Action;

use CopeX\VatValidationFrontend\Helper\Data;
use Magento\Customer\Api\AddressRepositoryInterface;
use Magento\Customer\Helper\Address;
use Magento\Customer\Model\Address\Mapper;
use Magento\Framework\Api\SearchCriteriaBuilder;
use Magento\Framework\App\RequestInterface;
use Magento\Framework\App\ResponseInterface;
use Magento\Framework\Exception\ValidatorException;
use Magento\Framework\Serialize\Serializer\Json;
use Magento\Multishipping\Controller\Checkout\CheckItems;

class MultishippingCheckItems
{
    /**
     * @var Data
     */
    private $helper;

    /**
     * @var Address
     */
    private $customerAddressHelper;

    /**
     * @var Mapper
     */
    private $addressMapper;

    /**
     * @var RequestInterface
     */
    private $request;

    /**
     * @var ResponseInterface
     */
    private $response;

    /**
     * @var Json
     */
    private $json;

    /**
     * @var AddressRepositoryInterface
     */
    private $addressRepository;

    /**
     * @var SearchCriteriaBuilder
     */
    private $searchCriteriaBuilder;

    /**
     * MultishippingCheckItems constructor.
     * @param Data $helper
     * @param Address $customerAddressHelper
     * @param Mapper $addressMapper
     * @param RequestInterface $request
     * @param ResponseInterface $response
     * @param Json $json
     * @param AddressRepositoryInterface $addressRepository
     * @param SearchCriteriaBuilder $searchCriteriaBuilder
     */
    public function __construct(
        Data $helper,
        Address $customerAddressHelper,
        Mapper $addressMapper,
        RequestInterface $request,
        ResponseInterface $response,
        Json $json,
        AddressRepositoryInterface $addressRepository,
        SearchCriteriaBuilder $searchCriteriaBuilder
    ) {
        $this->helper = $helper;
        $this->customerAddressHelper = $customerAddressHelper;
        $this->addressMapper = $addressMapper;
        $this->request = $request;
        $this->response = $response;
        $this->json = $json;
        $this->addressRepository = $addressRepository;
        $this->searchCriteriaBuilder = $searchCriteriaBuilder;
    }

    public function aroundExecute(CheckItems $subject, callable $proceed)
    {
        if (!$this->helper->isEnabled()) {
            return $proceed();
        }
        $newAddress = $this->request->getPost('new_address');
        $continue = $this->request->getPost('continue');
        $info = $this->request->getPost('ship');
        if ($newAddress || !$continue || !is_array($info)) {
            return $proceed();
        }
        $addressIds = [];
        foreach ($info as $itemData) {
            foreach ($itemData as $quoteItemId => $data) {
                if (empty($data['address'])) {
                    continue;
                }
                $addressIds[$data['address']] = $data['address'];
            }
        }
        $criteria = $this->searchCriteriaBuilder
            ->addFilter('entity_id', implode(',', $addressIds), 'in')
            ->create();
        $addresses = $this->addressRepository->getList($criteria);
        try {
            $success = true;
            $this->validateVat($addresses->getItems());
        } catch (ValidatorException $e) {
            $success = false;
            $this->response->representJson(
                $this->json->serialize([
                    'success' => false,
                    'error_message' => $e->getMessage(),
                ])
            );
        }
        if ($success) {
            return $proceed();
        }
    }

    /**
     * @param $addresses
     * @throws ValidatorException
     */
    private function validateVat($addresses)
    {
        foreach ($addresses as $address) {
            if ($this->helper->validateAddress($address)) {
                continue;
            }
            $renderer = $this->customerAddressHelper->getFormatTypeRenderer('oneline');
            $result = $renderer->renderArray($this->addressMapper->toFlatArray($address));
            throw new ValidatorException(__(
                'Please enter a valid VAT number in the following address: %1',
                $result
            ));
        }
    }
}
