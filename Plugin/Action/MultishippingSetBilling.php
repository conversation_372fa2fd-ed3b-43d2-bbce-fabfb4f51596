<?php

declare(strict_types=1);

namespace CopeX\VatValidationFrontend\Plugin\Action;

use CopeX\VatValidationFrontend\Helper\Data;
use Magento\Customer\Api\AddressRepositoryInterface;
use Magento\Framework\App\RequestInterface;
use Magento\Framework\App\Response\RedirectInterface;
use Magento\Framework\App\ResponseInterface;
use Magento\Framework\Message\ManagerInterface;
use Magento\Multishipping\Controller\Checkout\Address\SetBilling;

class MultishippingSetBilling
{
    /**
     * @var Data
     */
    private $helper;

    /**
     * @var RequestInterface
     */
    private $request;

    /**
     * @var ResponseInterface
     */
    private $response;

    /**
     * @var ManagerInterface
     */
    private $messageManager;

    /**
     * @var RedirectInterface
     */
    private $redirect;

    /**
     * @var AddressRepositoryInterface
     */
    private $addressRepository;

    /**
     * MultishippingSetBilling constructor.
     * @param Data $helper
     * @param RequestInterface $request
     * @param ResponseInterface $response
     * @param ManagerInterface $messageManager
     * @param RedirectInterface $redirect
     * @param AddressRepositoryInterface $addressRepository
     */
    public function __construct(
        Data $helper,
        RequestInterface $request,
        ResponseInterface $response,
        ManagerInterface $messageManager,
        RedirectInterface $redirect,
        AddressRepositoryInterface $addressRepository
    ) {
        $this->helper = $helper;
        $this->request = $request;
        $this->response = $response;
        $this->messageManager = $messageManager;
        $this->redirect = $redirect;
        $this->addressRepository = $addressRepository;
    }

    /**
     * @param SetBilling $subject
     * @param callable $proceed
     * @return mixed
     */
    public function aroundExecute(SetBilling $subject, callable $proceed)
    {
        if (!$this->helper->isEnabled()) {
            return $proceed();
        }
        $addressId = $this->request->getParam('id');
        if (!$addressId) {
            return $proceed();
        }
        try {
            $address = $this->addressRepository->getById($addressId);
        } catch (\Exception $e) {
            return $proceed();
        }
        if ($this->helper->validateAddress($address)) {
            return $proceed();
        }
        $this->messageManager->addErrorMessage(__('Please enter a valid VAT number.'));
        $this->redirect->redirect(
            $this->response,
            $this->redirect->getRedirectUrl()
        );
    }
}
