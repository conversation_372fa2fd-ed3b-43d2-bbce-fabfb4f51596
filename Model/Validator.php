<?php

declare(strict_types=1);

namespace CopeX\VatValidationFrontend\Model;

use Magento\Customer\Model\Vat;
use Magento\Framework\Exception\RemoteServiceUnavailableException;
use Magento\Framework\Exception\ValidatorException;

class Validator
{
    /**
     * @var Vat
     */
    private $customerVat;


    /**
     * Validator constructor.
     * @param Vat $customerVat
     */
    public function __construct(
        Vat $customerVat
    ) {
        $this->customerVat = $customerVat;
    }

    /**
     * @param $countryCode
     * @param $vatNumber
     * @return bool
     * @throws RemoteServiceUnavailableException
     */
    public function isValid($countryCode, $vatNumber)
    {
        if (!$countryCode || !$vatNumber) {
            return false;
        }

        return $this->fetchIsValid($countryCode, $vatNumber);
    }

    /**
     * @param $countryCode
     * @param $vatNumber
     * @return mixed
     * @throws RemoteServiceUnavailableException | \Exception
     */
    private function fetchIsValid($countryCode, $vatNumber)
    {
        $result = $this->customerVat->checkVatNumber($countryCode, $vatNumber);
        if (!$result->getRequestSuccess()) {
            throw new RemoteServiceUnavailableException(
                __("VAT validation service is not available")
            );
        }
        if(!$result->getIsValid() && isset($result->getResponse()['qualified']['error'])){
            $errorMsg = (string) __('Please enter a valid VAT number.');
            foreach( $result->getResponse()['qualified']['error'] as $errorMessage){
                $errorMsg .= "</br>" . __($errorMessage);
            }
            if($errorMsg){
                throw new \Exception($errorMsg);
            }
        }

        return $result->getIsValid();
    }

}
