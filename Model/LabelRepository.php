<?php


namespace Cope<PERSON>\Plc\Model;

use CopeX\Plc\Model\ResourceModel\Label\CollectionFactory as LabelCollectionFactory;
use Magento\Framework\Api\ExtensibleDataObjectConverter;
use Magento\Framework\Reflection\DataObjectProcessor;
use CopeX\Plc\Api\Data\LabelInterfaceFactory;
use Magento\Framework\Exception\CouldNotDeleteException;
use CopeX\Plc\Model\ResourceModel\Label as ResourceLabel;
use Magento\Framework\Api\DataObjectHelper;
use CopeX\Plc\Api\Data\LabelSearchResultsInterfaceFactory;
use Magento\Framework\Exception\CouldNotSaveException;
use Magento\Framework\Api\SearchCriteria\CollectionProcessorInterface;
use Magento\Framework\Exception\NoSuchEntityException;
use CopeX\Plc\Api\LabelRepositoryInterface;
use Magento\Store\Model\StoreManagerInterface;
use Magento\Framework\Api\ExtensionAttribute\JoinProcessorInterface;

class LabelRepository implements LabelRepositoryInterface
{

    protected $dataObjectProcessor;

    protected $dataObjectHelper;

    protected $labelCollectionFactory;

    protected $dataLabelFactory;

    protected $resource;

    protected $labelFactory;

    protected $searchResultsFactory;

    private $collectionProcessor;

    protected $extensibleDataObjectConverter;

    protected $extensionAttributesJoinProcessor;

    protected $storeManager;


    /**
     * @param ResourceLabel $resource
     * @param LabelFactory $labelFactory
     * @param LabelInterfaceFactory $dataLabelFactory
     * @param LabelCollectionFactory $labelCollectionFactory
     * @param LabelSearchResultsInterfaceFactory $searchResultsFactory
     * @param DataObjectHelper $dataObjectHelper
     * @param DataObjectProcessor $dataObjectProcessor
     * @param StoreManagerInterface $storeManager
     * @param CollectionProcessorInterface $collectionProcessor
     * @param JoinProcessorInterface $extensionAttributesJoinProcessor
     * @param ExtensibleDataObjectConverter $extensibleDataObjectConverter
     */
    public function __construct(
        ResourceLabel $resource,
        LabelFactory $labelFactory,
        LabelInterfaceFactory $dataLabelFactory,
        LabelCollectionFactory $labelCollectionFactory,
        LabelSearchResultsInterfaceFactory $searchResultsFactory,
        DataObjectHelper $dataObjectHelper,
        DataObjectProcessor $dataObjectProcessor,
        StoreManagerInterface $storeManager,
        CollectionProcessorInterface $collectionProcessor,
        JoinProcessorInterface $extensionAttributesJoinProcessor,
        ExtensibleDataObjectConverter $extensibleDataObjectConverter
    ) {
        $this->resource = $resource;
        $this->labelFactory = $labelFactory;
        $this->labelCollectionFactory = $labelCollectionFactory;
        $this->searchResultsFactory = $searchResultsFactory;
        $this->dataObjectHelper = $dataObjectHelper;
        $this->dataLabelFactory = $dataLabelFactory;
        $this->dataObjectProcessor = $dataObjectProcessor;
        $this->storeManager = $storeManager;
        $this->collectionProcessor = $collectionProcessor;
        $this->extensionAttributesJoinProcessor = $extensionAttributesJoinProcessor;
        $this->extensibleDataObjectConverter = $extensibleDataObjectConverter;
    }

    /**
     * {@inheritdoc}
     */
    public function save(
        \CopeX\Plc\Api\Data\LabelInterface $label
    ) {
        /* if (empty($label->getStoreId())) {
            $storeId = $this->storeManager->getStore()->getId();
            $label->setStoreId($storeId);
        } */
        
        $labelData = $this->extensibleDataObjectConverter->toNestedArray(
            $label,
            [],
            \CopeX\Plc\Api\Data\LabelInterface::class
        );
        
        $labelModel = $this->labelFactory->create()->setData($labelData);
        
        try {
            $this->resource->save($labelModel);
        } catch (\Exception $exception) {
            throw new CouldNotSaveException(__(
                'Could not save the label: %1',
                $exception->getMessage()
            ));
        }
        return $labelModel->getDataModel();
    }

    /**
     * {@inheritdoc}
     */
    public function getById($labelId)
    {
        $label = $this->labelFactory->create();
        $this->resource->load($label, $labelId);
        if (!$label->getId()) {
            throw new NoSuchEntityException(__('Label with id "%1" does not exist.', $labelId));
        }
        return $label->getDataModel();
    }

    /**
     * {@inheritdoc}
     */
    public function getList(
        \Magento\Framework\Api\SearchCriteriaInterface $criteria
    ) {
        $collection = $this->labelCollectionFactory->create();
        
        $this->extensionAttributesJoinProcessor->process(
            $collection,
            \CopeX\Plc\Api\Data\LabelInterface::class
        );
        
        $this->collectionProcessor->process($criteria, $collection);
        
        $searchResults = $this->searchResultsFactory->create();
        $searchResults->setSearchCriteria($criteria);
        
        $items = [];
        foreach ($collection as $model) {
            $items[] = $model->getDataModel();
        }
        
        $searchResults->setItems($items);
        $searchResults->setTotalCount($collection->getSize());
        return $searchResults;
    }

    /**
     * {@inheritdoc}
     */
    public function delete(
        \CopeX\Plc\Api\Data\LabelInterface $label
    ) {
        try {
            $this->resource->delete($label);
        } catch (\Exception $exception) {
            throw new CouldNotDeleteException(__(
                'Could not delete the Label: %1',
                $exception->getMessage()
            ));
        }
        return true;
    }

    /**
     * {@inheritdoc}
     */
    public function deleteById($labelId)
    {
        return $this->delete($this->getById($labelId));
    }
}
