<?php

declare(strict_types=1);

namespace CopeX\Plc\Model\Service;

use CopeX\Plc\Model\Postpay\NoPostPayException;
use CopeX\Plc\Model\Postpay\PostPayResponseException;
use CopeX\PlcLib\Client;
use CopeX\PlcLib\Config;
use CopeX\PlcLib\PostPayRefund;
use CopeX\PlcLib\Response;
use CopeX\Plc\Helper\Config;
use CopeX\Plc\Helper\Data;
use Magento\Sales\Model\Order;
use Magento\Sales\Model\Order\Creditmemo;
use Magento\Sales\Model\Order\Shipment;
use Magento\Sales\Model\ResourceModel\Order\Shipment\Track\CollectionFactory as TrackCollectionFactory;
use Psr\Log\LoggerInterface;

/**
 * PostPay refund service
 */
class PostPayRefundService
{
    const string COPEX_PLC_POSTPAY_REFUND_ENABLED = 'copex_plc/postpay_refund/enabled';
    const string COPEX_PLC_POSTPAY_REFUND_USERNAME = 'copex_plc/postpay_refund/username';
    const string COPEX_PLC_POSTPAY_REFUND_PASSWORD = 'copex_plc/postpay_refund/password';

    public function __construct(
        private readonly Config $config,
        private readonly Data $data,
        private readonly TrackCollectionFactory $trackCollectionFactory,
        private readonly LoggerInterface $logger
    ) {
    }

    /**
     * Process PostPay refund for credit memo
     */
    public function processRefund(Creditmemo $creditMemo)
    {
        $order = $creditMemo->getOrder();
        
        if (!$this->isPostPayRefundEnabled((int)$order->getStoreId())) {
            return;
        }

        try {
            $trackingNumbers = $this->getCreditMemoItemsByTrackingNumber($creditMemo);
            
            if (empty($trackingNumbers)) {
                throw new NoPostPayException('No tracking numbers found for PostPay refund', ['order' => $order, 'creditmemo' => $creditMemo]);
            }

            if(count($trackingNumbers) === 1){
                $refundAmount = $creditMemo->getBaseGrandTotal();
                $this->createRefundForTrackingNumber($order, $creditMemo, array_keys($trackingNumbers)[0], $refundAmount);
            } else {
                foreach ($trackingNumbers as $trackingNumber => $items) {
                    $refundAmount = $this->calculateRefundAmount($items);
                    $this->createRefundForTrackingNumber($order, $creditMemo, $trackingNumber, $refundAmount);
                }
            }
        } catch (\Exception $e) {
            $this->logger->error('PostPay refund processing failed', [
                'order_id' => $order->getIncrementId(),
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ]);
            throw $e;
        }
    }

    private function createRefundForTrackingNumber(
        Order $order,
        Creditmemo $creditMemo,
        string $trackingNumber,
        float $refundAmount
    ) {
        if ($refundAmount <= 0) {
            return false;
        }

        $refund = $this->buildPostPayRefund($order, $creditMemo, $trackingNumber, $refundAmount);
        $result = $this->executeRefund($refund, (int)$order->getStoreId());

        if (!$result->isSuccess()) {
            throw new PostPayResponseException($result->getErrorMessage(), $result);
        } else {
            $this->logger->info('PostPay refund successful', [
                'order_id' => $order->getIncrementId(),
                'tracking_number' => $trackingNumber,
                'refund_amount' => $refundAmount,
            ]);
        }
        return true;
    }


    /**
     * Check if refund should be processed for this order
     */
    public function shouldProcessRefund(Order $order): bool
    {
        return $this->data->isPostPayOrder($order) && $this->isPostPayRefundEnabled((int)$order->getStoreId());
    }

    /**
     * Check if PostPay refund is enabled
     */
    public function isPostPayRefundEnabled(?int $storeId = null): bool
    {
        return $this->config->getStoreConfig(self::COPEX_PLC_POSTPAY_REFUND_ENABLED, $storeId) === '1';
    }

    /**
     * Get tracking numbers for order
     */
    private function getCreditMemoItemsByTrackingNumber(Creditmemo $creditMemo): array
    {
        $trackingNumbers = [];
        $creditMemoItems = $creditMemo->getItems();
        $order = $creditMemo->getOrder();
        $labels = $this->getLabelsByOrder($order);
        foreach ($order->getShipmentsCollection() as $shipment) {
            /** @var Shipment $shipment */
            $tracks = $this->trackCollectionFactory->create()
                ->setShipmentFilter($shipment->getId());
                
            foreach ($tracks as $track) {
                if ($track->getTrackNumber() && $track->getCarrierCode() === \CopeX\Plc\Model\Carrier\AustrianPost::CARRIER_CODE) {
                    foreach($creditMemoItems as $item) {
                        foreach($shipment->getItems() as $shipmentItem){
                            if ($item->getOrderItem()->getId() === $shipmentItem->getOrderItemId()) {
                                $trackingNumbers[$track->getTrackNumber()][] = $item;
                            }
                        }
                    }
                }
            }
        }
        
        return $trackingNumbers;
    }

    /**
     * Calculate refund amount per tracking number
     */
    private function calculateRefundAmount($items): float
    {
        $refundAmount = 0;
        foreach ($items as $item) {
            $refundAmount += $item->getBaseRowTotalInclTax();
        }
        return $refundAmount;
    }

    /**
     * Build PostPay refund object
     */
    private function buildPostPayRefund(
        Order $order, 
        Creditmemo $creditMemo, 
        string $trackingNumber, 
        float $refundAmount
    ): PostPayRefund {
        $shippingAddress = $order->getShippingAddress();
        
        return new PostPayRefund(
            trackingNumber: $trackingNumber,
            refundAmount: $refundAmount,
            consigneeFirstName: $shippingAddress->getFirstname(),
            consigneeLastName: $shippingAddress->getLastname(),
            consigneeStreet: implode(' ', $shippingAddress->getStreet()),
            consigneePostalCode: $shippingAddress->getPostcode(),
            consigneeCity: $shippingAddress->getCity(),
            consigneeEmail: $shippingAddress->getEmail() ?: $order->getCustomerEmail(),
            reasonForRefund: 'Refund for order ' . $order->getIncrementId()
        );
    }

    /**
     * Execute the refund via PostPay API
     */
    private function executeRefund(PostPayRefund $refund, ?int $storeId = null): Response
    {
        $client = $this->createPlcClient($storeId);
        return $client->createPostPayRefund($refund);
    }

    /**
     * Create PLC client with PostPay configuration
     */
    private function createPlcClient(?int $storeId = null): Client
    {
        $config = new Config(
            clientId: $this->config->getClientId($storeId),
            orgUnitId: $this->config->getOrgUnitId($storeId),
            orgUnitGuid: $this->config->getOrgUnitGuid($storeId),
            testMode: !$this->config->getIsProductionMode($storeId),
            labelFormat: $this->config->getStoreConfig(Config::XML_PATH_PRINTING_FORMAT, $storeId),
            language: $this->config->getStoreConfig(Config::XML_PATH_PRINTING_TYPE, $storeId),
            paperLayout: $this->config->getStoreConfig(Config::XML_PATH_PRINTING_LAYOUT, $storeId),
            postPayUsername: $this->config->getStoreConfig(self::COPEX_PLC_POSTPAY_REFUND_USERNAME, $storeId),
            postPayPassword: $this->config->getStoreConfig(self::COPEX_PLC_POSTPAY_REFUND_PASSWORD, $storeId)
        );

        return new Client($config);
    }

    public function getLabelsByOrder(Order $order)
    {
        return $this->data->getLabelsByOrderId($order->getId())->addFieldToFilter('packet_option', ['nin' => array_keys(Client::getReturnServices())]);
    }

    public function trackingExists(Order $order): bool
    {
        return $this->getLabelsByOrder($order)->count() > 0;
    }
}
