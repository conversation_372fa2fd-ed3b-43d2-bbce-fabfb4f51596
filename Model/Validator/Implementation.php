<?php

declare(strict_types = 1);

namespace CopeX\VatValidatorAustria\Model\Validator;

use Cope<PERSON>\VatValidator\Helper\Config;
use CopeX\VatValidator\Model\Validator\RateLimitInterface;
use CopeX\VatValidator\Model\Validator\RequestInterface;
use Cope<PERSON>\VatValidator\Model\Validator\ResultInterface;
use CopeX\VatValidator\Model\Validator\ValidatorInterface;
use CopeX\VatValidator\Model\Validator\ResultFactory;
use Magento\Framework\Exception\LocalizedException;

class Implementation implements ValidatorInterface, RateLimitInterface
{

    const CZECH_REPUBLIC_CODE = 'CZ';
    const SLOVAKIA_CODE = 'SK';

    const API_TYPE = 'finanzonline';
    const XML_PATH_API_URL = 'copex_vat_validator/finanzonline/api_url';
    const XML_PATH_API_LOGIN_URL = 'copex_vat_validator/finanzonline/api_login_url';
    const XML_PATH_PARTICIPANT_ID = 'copex_vat_validator/finanzonline/participant_id';
    const XML_PATH_USER_ID = 'copex_vat_validator/finanzonline/user_id';
    const XML_PATH_PIN_CODE = 'copex_vat_validator/finanzonline/pin_code';
    const XML_PATH_MANUFACTURER_VAT = 'copex_vat_validator/finanzonline/manufacturer_vat';
    private Config $config;
    private ResultFactory $resultFactory;

    public function __construct(Config $config, ResultFactory $resultFactory)
    {
        $this->config = $config;
        $this->resultFactory = $resultFactory;
    }

    /**
     * @param $isCzSvk
     * @return int
     */
    public function getStufe($vatID): int
    {
        return $this->checkCzechiaSlovakia($vatID) ? 1 : 2;
    }

    /**
     * @param $vatNumber
     * @param $gatewayResponse
     * @return mixed
     * @throws \Magento\Framework\Exception\AlreadyExistsException
     * @throws \Magento\Framework\Exception\LocalizedException
     */
    protected function validate(RequestInterface $request)
    {
        $result =  $this->resultFactory->create(['data' => [
            'is_valid' => false,
            'request_identifier' => strval(time()),
            'request_success' => false,
            'api_type' => self::API_TYPE,
            'vat_id'   => $request->getVatId()
        ]]);
        $apiResult = $this->callApi($request->getVatId());
        if (!empty($apiResult) && isset($apiResult['rc'])) {
            if ($apiResult['rc'] == 0) {
                preg_match('/^(.*)-(\d+)\s+(.*)$/', $apiResult['adrz2'], $addressMatches);
                $result->addData([
                    'is_valid' => true,
                    'request_success' => true,
                    'company'  => $apiResult['name'] ?? "",
                    'postcode' => $addressMatches[2] ?? $apiResult['adrz3'] ?? "",
                    'city'     => $addressMatches[3] ?? $apiResult['adrz4'] ?? "",
                    'street'   => $apiResult['adrz1'] ?? "",
                    'address'  => ($apiResult['adrz1'] ?? "") . ", " .  ($apiResult['adrz2'] ?? ""),
                    'country'  => $addressMatches[1] ?? substr($request->getVatId(), 0, 2),
                    'response' => $apiResult
                ]);
            } else {
                if ($apiResult['rc'] == -1 ||
                    $apiResult['rc'] == -2 ||
                    $apiResult['rc'] == -4 ||
                    $apiResult['rc'] == 1511 ||
                    $apiResult['rc'] == 1512 ||
                    $apiResult['rc'] == 1513 ||
                    $apiResult['rc'] == 1514
                ) {
                } else {
                    $result->setData('request_success', true);
                }
            }
        }

        return $result;
    }

    /**
     * @param $vatNumber
     * @return bool
     */
    private function checkCzechiaSlovakia($vatNumber)
    {
        $countryCode = substr($vatNumber, 0, 2);
        if ($countryCode == self::CZECH_REPUBLIC_CODE || $countryCode == self::SLOVAKIA_CODE) {
            return true;
        }

        return false;
    }

    /**
     * @param $vatId
     * @param $isCzSvk
     * @return array|mixed
     * @throws LocalizedException
     */
    public function callApi($vatId)
    {
        $apiUrl = $this->config->getConfigValue(self::XML_PATH_API_URL);
        $apiLoginUrl = $this->config->getConfigValue(self::XML_PATH_API_LOGIN_URL);
        $participantId = $this->config->getConfigValue(self::XML_PATH_PARTICIPANT_ID);
        $userId = $this->config->getConfigValue(self::XML_PATH_USER_ID);
        $pinCode = $this->config->getConfigValue(self::XML_PATH_PIN_CODE);
        $manufacturerVat = $this->config->getConfigValue(self::XML_PATH_MANUFACTURER_VAT);

        try {
            //Login process
            $validationResult = [];
            $soapClient = $this->getSoapClient($apiLoginUrl);
            $loginData = [
                "tid"          => $participantId,
                "benid"        => $userId,
                "pin"          => $pinCode,
                "herstellerid" => $manufacturerVat,
            ];
            $loginResult = $soapClient->__soapCall("login", ["loginRequest" => $loginData]);
            if ($loginResult && isset($loginResult->id)) {
                //Vat validation process
                $soapClientMain = $this->getSoapClient($apiUrl);
                $sessionId = $loginResult->id;
                $stufe = $this->getStufe($vatId);
                $requestData = [
                    "tid"    => $participantId,
                    "benid"  => $userId,
                    "id"     => $sessionId,
                    "uid_tn" => $manufacturerVat,
                    "uid"    => $vatId,
                    "stufe"  => $stufe,
                ];
                $validationResponse = $soapClientMain->__soapCall("uidAbfrage", ["uidAbfrageRequest" => $requestData]);
                if (!empty($validationResponse)) {
                    $validationResult = json_decode(json_encode($validationResponse), true);
                }
                //Logout process
                $logoutData = [
                    "tid"   => $participantId,
                    "benid" => $userId,
                    "id"    => $sessionId,
                ];
                $soapClient->__soapCall("logout", ["logoutRequest" => $logoutData]);
            }

            return $validationResult;
        } catch (\Exception $e) {
            throw new LocalizedException(__($e->getMessage()));
        }
    }

    /**
     * @param RequestInterface $request
     * @return ResultInterface
     * @throws LocalizedException
     * @throws \Magento\Framework\Exception\AlreadyExistsException
     */
    public function validateVatRequest(RequestInterface $request)
    {
        return $this->validate($request);
    }

    public function getName()
    {
        return "Finanz Online";
    }

    /**
     * @param mixed $apiLoginUrl
     * @return \SoapClient
     * @throws \SoapFault
     */
    private function getSoapClient(mixed $apiLoginUrl): \SoapClient
    {
        $soapClient = new \SoapClient($apiLoginUrl);
        $headers = [];
        $headers[] = new \SoapHeader($apiLoginUrl, 'Content-Type', 'text/xml');
        $headers[] = new \SoapHeader($apiLoginUrl, 'SOAPAction', '#POST');
        $soapClient->__setSoapHeaders($headers);
        return $soapClient;
    }

    /**
     * @return int Only allowed to request the same uid 2x per day so cache min cache time is 12 hours
     */
    public function getMinCacheTime()
    {
        return 43200;
    }

    public function getApiType()
    {
        return self::API_TYPE;
    }
}
