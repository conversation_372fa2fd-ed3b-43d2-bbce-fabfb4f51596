<?php

declare(strict_types=1);

namespace CopeX\PlcLib;

use SoapClient;
use SoapFault;
use stdClass;

/**
 * Main client for PLC API operations
 */
class Client
{
    // Business Mail
    public const BUSINESS_MAIL_INT = 89;
    public const BUSINESS_MAIL_INT_SELECT = 90;

    // Combi-freight
    public const COMBI_FREIGHT_INTERNATIONAL = 49;
    public const COMBI_FREIGHT_SHIPPING = 47;

    // Feldpost
    public const FELDPOST = 56;

    // Kleinpaket
    public const SMALL_SHIPPING = 96;
    public const SMALL_SHIPPING_PLUS = 16;

    // Next Day
    public const NEXT_DAY_SHIPPING = 65;

    // Paket International
    public const INTERNATIONAL = 70;

    // Paket Österreich
    public const STANDARD_SHIPPING = 10;

    // Paket Premium International
    public const INTERNATIONAL_PREMIUM = 45;

    // Paket Premium Österreich
    public const PREMIUM_SHIPPING = 31;

    // Paket Premium select Österreich
    public const PACKET_PREMIUM_SELECT_SHIPPING = 30;

    // Post Express
    public const EXPRESS_INTERNATIONAL = 46;
    public const EXPRESS_SHIPPING = 1;

    // Retourpaket
    public const RETURN_PARCEL = 28;
    public const RETURN_PARCEL_INTERNATIONAL = 63;
    public const RETURN_PARCEL_INTERNATIONAL_ABROAD = 4;
    public const RETURN_PARCEL_INTERNATIONAL_STANDARD = 66;

    // Same Day
    public const SAME_DAY = 64;

    // Wertpaket
    public const VALUE_PACKAGE_PREMIUM = 97;

    // Zeitfenster
    public const TIME_WINDOW = 15;

    private ?SoapClient $soapClient = null;

    public function __construct(
        private readonly Config $config,
        private readonly ?SoapClient $customSoapClient = null
    ) {
    }

    /**
     * Create a shipping label
     */
    public function createLabel(Shipment $shipment): Response
    {
        try {
            if (!$shipment->hasPackages()) {
                throw new PlcException('Shipment must have at least one package');
            }


            // Build SOAP request
            $request = $this->buildShipmentRequest($shipment);

            // Call SOAP service
            $soapClient = $this->getSoapClient();
            $response = $soapClient->ImportShipment($request);

            return $this->processShipmentResponse($response);
        } catch (SoapFault $e) {
            return Response::error(
                errorMessage: $e->getMessage(),
                errorCode: $e->getCode() ? (string)$e->getCode() : null
            );
        } catch (PlcException $e) {
            return Response::error(
                errorMessage: $e->getMessage()
            );
        }
    }

    /**
     * Cancel a shipping label
     */
    public function cancelLabel(string $trackingNumber): Response
    {
        try {
            $soapClient = $this->getSoapClient();

            $request = $this->buildCancelRequest($trackingNumber);
            $response = $soapClient->CancelShipments($request);

            return $this->processCancelResponse($response);
        } catch (SoapFault $e) {
            return Response::error(
                errorMessage: $e->getMessage(),
                errorCode: $e->getCode() ? (string)$e->getCode() : null
            );
        } catch (PlcException $e) {
            return Response::error(
                errorMessage: $e->getMessage()
            );
        }
    }

    /**
     * Perform end of day operation
     */
    public function performEndOfDay(): Response
    {
        try {
            $soapClient = $this->getSoapClient();

            $request = $this->buildEndOfDayRequest();
            $response = $soapClient->PerformEndOfDay($request);

            return $this->processEndOfDayResponse($response);
        } catch (SoapFault $e) {
            return Response::error(
                errorMessage: $e->getMessage(),
                errorCode: $e->getCode() ? (string)$e->getCode() : null
            );
        } catch (PlcException $e) {
            return Response::error(
                errorMessage: $e->getMessage()
            );
        }
    }

    /**
     * Get available delivery service options
     */
    public static function getDeliveryServices(): array
    {
        return [
            // Business Mail
            self::BUSINESS_MAIL_INT              => 'Business Mail International',
            self::BUSINESS_MAIL_INT_SELECT       => 'Business Mail International Select',

            // Combi-freight
            self::COMBI_FREIGHT_INTERNATIONAL    => 'Combi-freight International',
            self::COMBI_FREIGHT_SHIPPING         => 'Combi-freight Österreich',

            // Feldpost
            self::FELDPOST                       => 'Feldpostpaket',

            // Kleinpaket
            self::SMALL_SHIPPING                 => 'Kleinpaket 2000',
            self::SMALL_SHIPPING_PLUS            => 'Kleinpaket 2000 Plus',

            // Next Day
            self::NEXT_DAY_SHIPPING              => 'Next Day',

            // Paket International
            self::INTERNATIONAL                  => 'Paket Plus Int. Outbound (Paket International)',

            // Paket Österreich
            self::STANDARD_SHIPPING              => 'Paket Österreich (Paket Standard)',

            // Paket Premium International
            self::INTERNATIONAL_PREMIUM          => 'Paket Premium Int. Outbound B2B (Paket Premium International)',

            // Paket Premium Österreich
            self::PREMIUM_SHIPPING               => 'Paket Premium Österreich B2B (Paket Premium)',

            // Paket Premium select Österreich
            self::PACKET_PREMIUM_SELECT_SHIPPING => 'Paket Premium select Österreich',

            // Post Express
            self::EXPRESS_INTERNATIONAL          => 'Post Express International Outbound',
            self::EXPRESS_SHIPPING               => 'Post Express Österreich',

            // Same Day
            self::SAME_DAY                       => 'Same Day',

            // Wertpaket
            self::VALUE_PACKAGE_PREMIUM          => 'Wertpaket Premium',

            // Zeitfenster
            self::TIME_WINDOW                    => 'Zeitfensterzustellung',
        ];
    }

    /**
     * Get return delivery service options
     */
    public static function getReturnServices(): array
    {
        return [
            self::RETURN_PARCEL                        => 'Retourpaket',
            self::RETURN_PARCEL_INTERNATIONAL          => 'Retourpaket International',
            self::RETURN_PARCEL_INTERNATIONAL_ABROAD   => 'Retourpaket International Abgabe Ausland',
            self::RETURN_PARCEL_INTERNATIONAL_STANDARD => 'Retourpaket International Standard',
        ];
    }

    public static function needsContract(int $serviceId): bool
    {
        $contractOnly = [
            self::BUSINESS_MAIL_INT,
            self::BUSINESS_MAIL_INT_SELECT,
            self::COMBI_FREIGHT_INTERNATIONAL,
            self::COMBI_FREIGHT_SHIPPING,
            self::FELDPOST,
            self::SMALL_SHIPPING,
            self::SMALL_SHIPPING_PLUS,
            self::NEXT_DAY_SHIPPING,
            self::PACKET_PREMIUM_SELECT_SHIPPING,
            self::RETURN_PARCEL_INTERNATIONAL,
            self::RETURN_PARCEL_INTERNATIONAL_ABROAD,
            self::RETURN_PARCEL_INTERNATIONAL_STANDARD,
            self::SAME_DAY,
            self::VALUE_PACKAGE_PREMIUM,
            self::TIME_WINDOW,
        ];

        return in_array($serviceId, $contractOnly, true);
    }

    /**
     * Get or create SOAP client
     */
    private function getSoapClient(): SoapClient
    {
        if ($this->customSoapClient !== null) {
            return $this->customSoapClient;
        }

        if ($this->soapClient === null) {
            $options = $this->config->getSoapOptions();
            $this->soapClient = new SoapClient($this->config->getWsdlUrl(), $options);
        }

        return $this->soapClient;
    }

    /**
     * Build shipment request for SOAP API
     */
    private function buildShipmentRequest(Shipment $shipment): stdClass
    {
        $request = new stdClass();
        $request->row = new stdClass();

        // Basic shipment data
        $row = $request->row;
        $row->ClientID = $this->config->getClientId();
        $row->OrgUnitID = $this->config->getOrgUnitId();
        $row->OrgUnitGuid = $this->config->getOrgUnitGuid();
        $row->DeliveryServiceThirdPartyID = $shipment->getDeliveryServiceId();

        // References
        if ($shipment->getReference1()) {
            $row->OUShipperReference1 = $shipment->getReference1();
        }
        if ($shipment->getReference2()) {
            $row->OUShipperReference2 = $shipment->getReference2();
        }

        // Addresses
        $row->OUShipperAddress = (object)$shipment->getShipper()->toSoapArray();
        $row->OURecipientAddress = (object)$shipment->getRecipient()->toSoapArray();

        // Packages
        $row->ColloList = [];
        foreach ($shipment->getPackages() as $package) {
            $row->ColloList[] = (object)$package->toSoapArray();
        }

        // Return label
        $row->CustomDataBit1 = $shipment->shouldAddReturnLabel();

        // Printer settings
        if ($this->config->getPaperLayout() !== 'no') {
            $row->PrinterObject = new stdClass();
            $row->PrinterObject->LabelFormatID = $this->config->getLabelFormat();
            $row->PrinterObject->LanguageID = $this->config->getLanguage();
            $row->PrinterObject->PaperLayoutID = $this->config->getPaperLayout();
        }

        // PostPay data
        if ($shipment->hasPostPayData()) {
            $postPayData = $shipment->getPostPayData();

            // Set ThirdPartyID to 201 for PostPay
            $row->ThirdPartyID = 201;

            // Set PostPay values according to documentation
            if (isset($postPayData['order_amount'])) {
                $row->Value1 = $postPayData['order_amount'];
            }
            if (isset($postPayData['currency'])) {
                $row->Value2 = $postPayData['currency'];
            }
            if (isset($postPayData['value3'])) {
                $row->Value3 = $postPayData['value3'];
            }

            // Additional PostPay fields
            if (isset($postPayData['customer_email'])) {
                $row->CustomerContactEmail = $postPayData['customer_email'];
            }
            if (isset($postPayData['merchant_order_code'])) {
                $row->MerchantOrderCode = $postPayData['merchant_order_code'];
            }
            if (isset($postPayData['merchant_brand_name'])) {
                $row->MerchantBrandName = $postPayData['merchant_brand_name'];
            }
            if (isset($postPayData['order_date_time'])) {
                $row->OrderDateTime = $postPayData['order_date_time'];
            }
        }

        return $request;
    }

    /**
     * Build cancel request for SOAP API
     */
    private function buildCancelRequest(string $trackingNumber): stdClass
    {
        $request = new stdClass();
        $request->shipments = [];

        $shipment = new stdClass();
        $shipment->ClientID = $this->config->getClientId();
        $shipment->OrgUnitID = $this->config->getOrgUnitId();
        $shipment->OrgUnitGuid = $this->config->getOrgUnitGuid();
        $shipment->Number = $trackingNumber;
        $shipment->ColloCodeList = [$trackingNumber];

        $request->shipments[] = $shipment;

        return $request;
    }

    /**
     * Build end of day request for SOAP API
     */
    private function buildEndOfDayRequest(): stdClass
    {
        $request = new stdClass();
        $request->clientID = $this->config->getClientId();
        $request->orgUnitID = $this->config->getOrgUnitId();
        $request->orgUnitGuid = $this->config->getOrgUnitGuid();

        return $request;
    }

    /**
     * Process shipment response from SOAP API
     */
    private function processShipmentResponse($response): Response
    {
        if (!$response || !isset($response->ImportShipmentResult)) {
            return Response::error($response->errorMessage ?: "Invalid response from API", $response->errorCode, json_decode(json_encode($response),true));
        }

        $result = $response->ImportShipmentResult;

        // Check for errors
        if (isset($result->ErrorCode) && $result->ErrorCode !== null) {
            $errorMessage = $result->ErrorMessage ?? "Unknown error";
            return Response::error($errorMessage, (string)$result->ErrorCode);
        }

        // Extract tracking number
        $trackingNumber = null;
        if (isset($result->ColloRow[0]->ColloCodeList->ColloCodeRow[0]->Code)) {
            $trackingNumber = $result->ColloRow[0]->ColloCodeList->ColloCodeRow[0]->Code;
        }

        if (!$trackingNumber) {
            return Response::error("No tracking number in response");
        }

        // Extract label content
        $labelContent = null;
        $labelType = null;

        if (isset($response->zplLabelData) && $response->zplLabelData) {
            $labelContent = $response->zplLabelData;
            $labelType = "zpl";
        } elseif (isset($response->pdfData) && $response->pdfData) {
            $labelContent = base64_decode($response->pdfData);
            $labelType = "pdf";
        }

        return Response::success(
            trackingNumber: $trackingNumber,
            labelContent: $labelContent,
            labelType: $labelType,
            rawResponse: (array)$response
        );
    }

    /**
     * Process cancel response from SOAP API
     */
    private function processCancelResponse($response): Response
    {
        $trackingNumber = "";
        if (!$response || !isset($response->CancelShipmentsResult)) {
            return Response::error("Invalid response from API");
        }

        $result = $response->CancelShipmentsResult;

        if (isset($result->CancelShipmentResult[0])) {
            $cancelResult = $result->CancelShipmentResult[0];

            if (isset($cancelResult->ErrorCode) && $cancelResult->ErrorCode !== null) {
                $errorMessage = $cancelResult->ErrorMessage ?? "Unknown error";
                return Response::error($errorMessage, (string)$cancelResult->ErrorCode);
            }
            $trackingNumber = $cancelResult->Number;
        }

        return Response::success($trackingNumber, null, null, (array)$response);
    }

    /**
     * Process end of day response from SOAP API
     */
    private function processEndOfDayResponse($response): Response
    {
        if (!$response) {
            return Response::error("Invalid response from API");
        }

        if (isset($response->ErrorMessage) && $response->ErrorMessage) {
            return Response::error($response->ErrorMessage);
        }

        $result = $response->PerformEndOfDayResult ?? null;

        if ($result === null) {
            return Response::error("End of day result is empty. No labels to process.");
        }

        if (empty($result)) {
            return Response::error("End of day result is empty");
        }

        $labelContent = base64_decode($result);
        $fileName = "EOD_" . date("Ymd");

        return Response::success(
            trackingNumber: $fileName,
            labelContent: $labelContent,
            labelType: "pdf",
            rawResponse: (array)$response
        );
    }

    /**
     * Create a PostPay refund
     */
    public function createPostPayRefund(PostPayRefund $refund): Response
    {
        try {
            $requestData = $refund->toArray();
            
            $ch = curl_init();
            curl_setopt_array($ch, [
                CURLOPT_URL => $this->config->getPostPayRefundUrl(),
                CURLOPT_RETURNTRANSFER => true,
                CURLOPT_POST => true,
                CURLOPT_USERPWD => $this->config->getPostPayUsername() . ":" . $this->config->getPostPayPassword(),
                CURLOPT_POSTFIELDS => json_encode($requestData),
                CURLOPT_HTTPHEADER => $this->buildPostPayHeaders(),
                CURLOPT_TIMEOUT => 30,
                CURLOPT_SSL_VERIFYPEER => true,
                CURLOPT_SSL_VERIFYHOST => 2,
            ]);

            $response = curl_exec($ch);
            $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
            $error = curl_error($ch);
            curl_close($ch);

            if ($error) {
                return Response::error("cURL error: " . $error);
            }

            return $this->processPostPayRefundResponse($response, $httpCode);
        } catch (\Exception $e) {
            return Response::error(
                errorMessage: $e->getMessage(),
                errorCode: $e->getCode() ? (string)$e->getCode() : null
            );
        }
    }

    /**
     * Build headers for PostPay API requests
     */
    private function buildPostPayHeaders(): array
    {
        $headers = [];
        
        $postPayHeaders = $this->config->getPostPayHeaders();
        foreach ($postPayHeaders as $key => $value) {
            $headers[] = $key . ': ' . $value;
        }
        
        return $headers;
    }

    /**
     * Process PostPay refund response
     */
    private function processPostPayRefundResponse(string $response, int $httpCode): Response
    {
        $responseData = json_decode($response, true);
        
        if ($httpCode === 200 || $httpCode === 202) {
            return Response::success(
                trackingNumber: 'REFUND_' . date('YmdHis'),
                labelContent: null,
                labelType: null,
                rawResponse: $responseData
            );
        }
        
        // Handle error responses
        $errorMessage = 'PostPay refund failed';
        $errorCode = (string)$httpCode;
        
        if ($responseData) {
            if (isset($responseData['fault']['faultstring'])) {
                $errorMessage = $responseData['fault']['faultstring'];
                if (isset($responseData['fault']['detail']['errorcode'])) {
                    $errorCode = $responseData['fault']['detail']['errorcode'];
                }
            } elseif (isset($responseData['error']['message'])) {
                $errorMessage = $responseData['error']['message'];
                if (isset($responseData['error']['code'])) {
                    $errorCode = $responseData['error']['code'];
                }
            }
        }
        
        return Response::error($errorMessage, $errorCode, $responseData);
    }
}