<?php

declare(strict_types=1);

namespace CopeX\PlcLib\Exception;

/**
 * Exception for API-related errors
 */
class ApiException extends PlcException
{
    public function __construct(
        string $message,
        private readonly ?string $errorCode = null,
        ?\Throwable $previous = null
    ) {
        parent::__construct($message, 0, $previous);
    }

    public function getErrorCode(): ?string
    {
        return $this->errorCode;
    }
}
