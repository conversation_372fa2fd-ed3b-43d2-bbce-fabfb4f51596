<?php

declare(strict_types=1);

namespace CopeX\PlcLib;

/**
 * Response from PLC API operations
 */
class Response
{
    public function __construct(
        private readonly bool $success,
        private readonly ?string $trackingNumber = null,
        private readonly ?string $labelContent = null,
        private readonly ?string $labelType = null,
        private readonly ?string $errorMessage = null,
        private readonly ?string $errorCode = null,
        private readonly ?array $rawResponse = null
    ) {}

    public function isSuccess(): bool
    {
        return $this->success;
    }

    public function getTrackingNumber(): ?string
    {
        return $this->trackingNumber;
    }

    public function getLabelContent(): ?string
    {
        return $this->labelContent;
    }

    public function getLabelType(): ?string
    {
        return $this->labelType;
    }

    public function getErrorMessage(): ?string
    {
        return $this->errorMessage;
    }

    public function getErrorCode(): ?string
    {
        return $this->errorCode;
    }

    public function getRawResponse(): ?array
    {
        return $this->rawResponse;
    }

    public function getFileName(): ?string
    {
        if (!$this->trackingNumber || !$this->labelType) {
            return null;
        }

        return "LABEL_{$this->trackingNumber}.{$this->labelType}";
    }

    /**
     * Create successful response
     */
    public static function success(
        string $trackingNumber,
        ?string $labelContent = null,
        ?string $labelType = null,
        ?array $rawResponse = null
    ): self {
        return new self(
            success: true,
            trackingNumber: $trackingNumber,
            labelContent: $labelContent,
            labelType: $labelType,
            rawResponse: $rawResponse
        );
    }

    /**
     * Create error response
     */
    public static function error(
        string $errorMessage,
        ?string $errorCode = null,
        ?array $rawResponse = null
    ): self {
        return new self(
            success: false,
            errorMessage: $errorMessage,
            errorCode: $errorCode,
            rawResponse: $rawResponse
        );
    }
}
