<?php

declare(strict_types=1);

namespace CopeX\PlcLib\Model;

use CopeX\PlcLib\Exception\PlcException;

/**
 * Address data for shipping
 */
class Address
{
    public function __construct(
        private readonly string $name1,
        private readonly string $street,
        private readonly string $city,
        private readonly string $postalCode,
        private readonly string $countryId,
        private readonly ?string $name2 = null,
        private readonly ?string $houseNumber = null,
        private readonly ?string $email = null,
        private readonly ?string $phone = null,
        private readonly ?string $vatId = null,
        private readonly int $thirdPartyId = 0
    ) {
        if (empty($this->name1)) {
            throw new PlcException('Name1 cannot be empty');
        }
        if (empty($this->street)) {
            throw new PlcException('Street cannot be empty');
        }
        if (empty($this->city)) {
            throw new PlcException('City cannot be empty');
        }
        if (empty($this->postalCode)) {
            throw new PlcException('Postal code cannot be empty');
        }
        if (empty($this->countryId)) {
            throw new PlcException('Country ID cannot be empty');
        }
    }

    public function getName1(): string
    {
        return $this->name1;
    }

    public function getName2(): ?string
    {
        return $this->name2;
    }

    public function getStreet(): string
    {
        return $this->street;
    }

    public function getHouseNumber(): ?string
    {
        return $this->houseNumber;
    }

    public function getCity(): string
    {
        return $this->city;
    }

    public function getPostalCode(): string
    {
        return $this->postalCode;
    }

    public function getCountryId(): string
    {
        return $this->countryId;
    }

    public function getEmail(): ?string
    {
        return $this->email;
    }

    public function getPhone(): ?string
    {
        return $this->phone;
    }

    public function getVatId(): ?string
    {
        return $this->vatId;
    }

    public function getThirdPartyId(): int
    {
        return $this->thirdPartyId;
    }

    /**
     * Parse house number from street if not provided
     */
    public function getParsedHouseNumber(): string
    {
        if ($this->houseNumber !== null) {
            return $this->houseNumber;
        }

        // Extract house number from street
        if (preg_match("/[0-9][\/\-0-9a-zA-Z]{0,19}/", $this->street, $matches)) {
            return $matches[0];
        }

        return '';
    }

    /**
     * Get street without house number
     */
    public function getStreetWithoutHouseNumber(): string
    {
        $houseNumber = $this->getParsedHouseNumber();
        if (empty($houseNumber)) {
            return $this->street;
        }

        $pos = strpos($this->street, $houseNumber);
        if ($pos !== false) {
            return trim(substr($this->street, 0, $pos));
        }

        return $this->street;
    }

    /**
     * Convert to array format for SOAP API
     */
    public function toSoapArray(): array
    {
        $street = $this->getStreetWithoutHouseNumber();
        $houseNumber = $this->getParsedHouseNumber();

        return [
            'Name1' => substr($this->name1, 0, 100),
            'Name2' => $this->name2 ? substr($this->name2, 0, 100) : '',
            'AddressLine1' => substr($street, 0, 100),
            'AddressLine2' => '',
            'HouseNumber' => substr($houseNumber, 0, 20),
            'City' => $this->city,
            'PostalCode' => $this->postalCode,
            'CountryID' => $this->countryId,
            'Email' => $this->email ?? '',
            'Tel1' => $this->phone ?? '',
            'VATID' => $this->vatId ?? '',
            'ThirdPartyID' => $this->thirdPartyId,
        ];
    }
}
