<?php

declare(strict_types=1);

namespace CopeX\PlcLib\Model;

/**
 * Package/item data
 */
class Package
{

    /**
     * @var Article[] $articles
     */
    private array $articles = [];

    public function __construct(
        private readonly ?float $weight = null,
        private readonly ?int $length = null,
        private readonly ?int $height = null,
        private readonly ?int $width = null,
    ) {
        if ($this->weight && $this->weight <= 0) {
            throw new PlcException('Weight must be positive');
        }
    }

    public function getWeight(): float
    {
        return $this->weight;
    }

    public function getLength(): ?int
    {
        return $this->length;
    }

    public function getHeight(): ?int
    {
        return $this->height;
    }

    public function getWidth(): ?int
    {
        return $this->width;
    }


    public function addArticle(Article $article): void
    {
        $this->articles[] = $article;
    }

    /**
     * @return Article[]
     */
    public function getArticles(): array
    {
        return $this->articles;
    }

    public function hasArticles(): bool
    {
        return !empty($this->articles);
    }

    /**
     * Convert to array format for SOAP API
     */
    public function toSoapArray(): array
    {
        if ($this->weight !== null) {
            $data = ['Weight' => $this->weight];
        }
        
        if ($this->length !== null) {
            $data['Length'] = $this->length;
        }
        if ($this->height !== null) {
            $data['Height'] = $this->height;
        }
        if ($this->width !== null) {
            $data['Width'] = $this->width;
        }
        if ($this->hasArticles()) {
            foreach($this->getArticles() as $article){
                $data['ColloArticleList'][] = $article->toSoapArray();
            }
        }

        return $data;
    }
}
