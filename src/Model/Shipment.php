<?php

declare(strict_types=1);

namespace CopeX\PlcLib\Model;

/**
 * Shipment data for label creation
 */
class Shipment
{
    /** @var Package[] */
    private array $packages = [];

    public function __construct(
        private readonly Address $shipper,
        private readonly Address $recipient,
        private readonly int $deliveryServiceId,
        private readonly ?string $reference1 = null,
        private readonly ?string $reference2 = null,
        private readonly bool $addReturnLabel = false,
        private readonly ?array $postPayData = null
    ) {
        if ($this->deliveryServiceId <= 0) {
            throw new PlcException('Delivery service ID must be positive');
        }
    }

    public function getShipper(): Address
    {
        return $this->shipper;
    }

    public function getRecipient(): Address
    {
        return $this->recipient;
    }

    public function getDeliveryServiceId(): int
    {
        return $this->deliveryServiceId;
    }

    public function getReference1(): ?string
    {
        return $this->reference1;
    }

    public function getReference2(): ?string
    {
        return $this->reference2;
    }

    public function shouldAddReturnLabel(): bool
    {
        return $this->addReturnLabel;
    }

    public function addPackage(Package $package): void
    {
        $this->packages[] = $package;
    }

    /**
     * @return Package[]
     */
    public function getPackages(): array
    {
        return $this->packages;
    }

    public function hasPackages(): bool
    {
        return !empty($this->packages);
    }

    /**
     * Check if this is a return shipment
     */
    public function isReturn(): bool
    {
        return in_array($this->deliveryServiceId, [28, 63, 66], true);
    }

    /**
     * Get PostPay data
     */
    public function getPostPayData(): ?array
    {
        return $this->postPayData;
    }

    /**
     * Check if this shipment has PostPay data
     */
    public function hasPostPayData(): bool
    {
        return $this->postPayData !== null && !empty($this->postPayData);
    }
}