<?php

declare(strict_types=1);

namespace CopeX\PlcLib\Model;

use CopeX\PlcLib\Exception\PlcException;

/**
 * Package/item data
 */
class Article
{
    public function __construct(
        private readonly ?float $weight = null,
        private readonly ?int $length = null,
        private readonly ?int $height = null,
        private readonly ?int $width = null
    ) {
        if (isset($this->weight) && $this->weight <= 0) {
            throw new PlcException('Weight must be positive');
        }
    }

    public function getWeight(): float
    {
        return $this->weight;
    }

    public function getLength(): ?int
    {
        return $this->length;
    }

    public function getHeight(): ?int
    {
        return $this->height;
    }

    public function getWidth(): ?int
    {
        return $this->width;
    }



    /**
     * Convert to array format for SOAP API
     */
    public function toSoapArray(): array
    {
        if ($this->weight !== null) {
            $data = ['Weight' => $this->weight];
        }
        
        if ($this->length !== null) {
            $data['Length'] = $this->length;
        }
        if ($this->height !== null) {
            $data['Height'] = $this->height;
        }
        if ($this->width !== null) {
            $data['Width'] = $this->width;
        }

        return $data;
    }
}
