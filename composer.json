{"name": "swissup/module-firecheckout", "description": "Onestep checkout extension for Magento2", "keywords": ["firecheckout", "onestep checkout", "checkout"], "type": "magento2-module", "version": "1.31.2-p1", "license": "OSL-3.0", "require": {"swissup/module-checkout": "^1.5.2", "swissup/module-firecheckout-integrations": "^1.4.7", "swissup/module-rtl": "^1.3.4", "swissup/module-stickyfill": "^1.2.2", "swissup/module-tippyjs": "^6.0.0", "swissup/module-codemirror": "^1.1.7"}, "autoload": {"files": ["registration.php"], "psr-4": {"Swissup\\Firecheckout\\": ""}}, "extra": {"swissup": {"links": {"identity_key": "https://www.firecheckout.net/license/customer/identity/", "store": "https://www.firecheckout.net/order", "download": "https://www.firecheckout.net/subscription/customer/products/", "docs": "http://docs.swissuplabs.com/m2/extensions/firecheckout/", "changelog": "http://docs.swissuplabs.com/m2/extensions/firecheckout/changelog/"}}, "marketplace": {"gallery": ["https://docs.swissuplabs.com/images/marketplace/module-firecheckout.png"], "links": {"docs": "https://docs.swissuplabs.com/m2/firecheckout/", "changelog": "https://docs.swissuplabs.com/m2/firecheckout/changelog/"}}}}