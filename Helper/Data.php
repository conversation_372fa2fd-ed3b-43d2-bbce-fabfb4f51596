<?php

declare(strict_types=1);

namespace CopeX\VatValidationFrontend\Helper;

use CopeX\VatValidationFrontend\Model\Validator;
use CopeX\VatValidator\Model\Validator as CoreValidator;
use Magento\Framework\App\Config\ScopeConfigInterface;
use Magento\Framework\Exception\RemoteServiceUnavailableException;
use Magento\Framework\Exception\ValidatorException;
use Magento\Framework\View\Element\Block\ArgumentInterface;
use Magento\Store\Model\ScopeInterface;

class Data implements ArgumentInterface
{
    /**
     * Path to store config is VAT field enabled
     *
     * @var string
     */
    const VAT_ENABLED = 'customer/create_account/vat_frontend_visibility';

    const XML_PATH_FRONTEND_ENABLED = 'copex_vat_validator/vat_validation_frontend/enable';
    /**
     * Path to store config validation method
     *
     * @var string
     */
    const XML_PATH_ENABLE_THROW_ERROR = 'copex_vat_validator/vat_validation_frontend/enable_throw_error';

    const XML_PATH_QUALIFIED = 'copex_vat_validator/vat_validation_frontend/qualified';
    const XML_PATH_CHECKOUT_TOOLTIP = 'copex_vat_validator/vat_validation_frontend/checkout_tooltip';
    const XML_PATH_CHECKOUT_DELAY = 'copex_vat_validator/vat_validation_frontend/delay';

    /**
     * @var Validator
     */
    private $validator;
    private CoreValidator $coreValidator;

    protected $scopeConfig;

    /**
     * Data constructor.
     * @param Validator $validator
     */
    public function __construct(
        Validator $validator,
        CoreValidator $coreValidator,
        ScopeConfigInterface $scopeConfig
    ) {
        $this->validator = $validator;
        $this->coreValidator = $coreValidator;
        $this->scopeConfig = $scopeConfig;
    }

    /**
     * @param $key
     * @return mixed
     */
    protected function getConfig($key)
    {
        return $this->scopeConfig->getValue($key, ScopeInterface::SCOPE_STORE);
    }

    public function isEnabled()
    {
        return (bool)$this->getConfig(\CopeX\VatValidator\Helper\Config::ENABLE);
    }

    /**
     * Check if VAT field is enabled in admin
     * @return boolean
     */
    public function isVatFieldEnabled()
    {
        return $this->isEnabled() && $this->getConfig(self::VAT_ENABLED);
    }

    public function getCheckoutTooltip()
    {
        return $this->getConfig(self::XML_PATH_CHECKOUT_TOOLTIP);
    }

    public function getInputDelay()
    {
        return $this->getConfig(self::XML_PATH_CHECKOUT_DELAY) ?? 3000;
    }

    /**
     * Check if both VAT field and validation are enabled
     * @return bool
     */
    public function canValidateVat()
    {
        return $this->isVatFieldEnabled();
    }


    /**
     * Check if ValidationError is enabled in admin
     * @return boolean
     */
    public function isShowErrorEnabled()
    {
        return $this->getConfig(self::XML_PATH_ENABLE_THROW_ERROR);
    }


    /**
     * @param $countryCode
     * @param $vatNumber
     * @return bool
     * @throws RemoteServiceUnavailableException
     */
    public function validateVat($countryCode, $vatNumber)
    {
        return $this->validator->isValid($countryCode, $vatNumber);
    }

    /**
     * @param $address
     * @param false $strict
     * @return bool
     */
    public function validateAddress($address)
    {
        if(!$address->getVatId()) return true;
        try {
            $this->coreValidator->setCurrentAddress($address);
            $isValid = $this->validateVat($address->getCountryId(), $address->getVatId());
        } catch (RemoteServiceUnavailableException $e) {
            $isValid = false;
        }
        $this->coreValidator->setCurrentAddress(null);

        return $isValid;
    }

    /**
     * @throws ValidatorException
     */
    public function showWarningMessage($address)
    {
        if ($this->isShowErrorEnabled()) {
            if (!$this->validateAddress($address)) {
                throw new ValidatorException(__('Please enter a valid VAT number.'));
            }
        }
    }

    public function qualifiedValidationInFrontend()
    {
        return (bool) $this->scopeConfig->getValue(self::XML_PATH_QUALIFIED);
    }

    public function isFrontendEnabled()
    {
        return (bool) $this->scopeConfig->getValue(self::XML_PATH_FRONTEND_ENABLED) && ! $this->otherModulesFrontendEnabled();
    }

    private function otherModulesFrontendEnabled()
    {
        //Geissweb
        return $this->scopeConfig->getValue("euvat/interface_settings/validate_vatid");
    }

    public function getValidationUrl()
    {
        return 'copex_vat_validator_frontend/index/validate';
    }
}
