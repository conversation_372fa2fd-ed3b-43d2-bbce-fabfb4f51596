<?php

namespace CopeX\ThemeGreen\ViewModel;

use Cope<PERSON>\ThemeGreen\Plugin\OriginalThemeStorage;
use Hyva\Theme\Service\CurrentTheme;
use Magento\Framework\View\Asset\Repository;
use Magento\Framework\View\Element\Block\ArgumentInterface;

class HyvaStyles implements ArgumentInterface
{
    private CurrentTheme $currentTheme;
    private Repository $assetRepository;
    private OriginalThemeStorage $originalThemeStorage;

    public function __construct(CurrentTheme $currentTheme, Repository $assetRepository, OriginalThemeStorage $originalThemeStorage)
    {
        $this->currentTheme = $currentTheme;
        $this->assetRepository = $assetRepository;
        $this->originalThemeStorage = $originalThemeStorage;
    }

    public function getStyles(){
        if (!$this->currentTheme->isHyva() && $this->originalThemeStorage->getIsOriginalThemeHyva()) {
            $viewFileContext = $this->assetRepository->getStaticViewFileContext();
            $originalTheme = $this->originalThemeStorage->getOriginalTheme();
            $currentTheme = $this->originalThemeStorage->getCurrentTheme();
            $originalAssetPath = str_replace($currentTheme->getFullPath(), $originalTheme->getFullPath(), $viewFileContext->getPath());
            $path = $viewFileContext->getBaseDirType().DIRECTORY_SEPARATOR.$originalAssetPath.DIRECTORY_SEPARATOR."css/";
            if (file_exists($path . "styles.min.css")) {
                return $path . "styles.min.css";
            }
            return $path . "styles.css";
        }
        return "";
    }
}