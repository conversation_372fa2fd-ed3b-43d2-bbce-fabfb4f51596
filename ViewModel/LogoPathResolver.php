<?php

namespace CopeX\HyvaTheme\ViewModel;

use Magento\Config\Model\Config\Backend\Image\Logo;
use Magento\Framework\UrlInterface;
use Magento\Store\Model\ScopeInterface;

class LogoPathResolver extends \Hyva\Theme\ViewModel\Logo\LogoPathResolver
{
    /**
     * Return logo image path
     *
     * @return string|null
     * @see \Magento\Theme\ViewModel\Block\Html\Header\LogoPathResolver::getPath
     */
    public function getMobilePath(): ?string
    {
        $path = null;
        $storeLogoPath = $this->_scopeConfig->getValue(
            'design/header/mobile_logo_src',
            ScopeInterface::SCOPE_STORE
        );
        if ($storeLogoPath !== null) {
            $path = Logo::UPLOAD_DIR . '/' . $storeLogoPath;
        }
        return $path;
    }

    /**
     * Override the parent block so the $logoFile value can be passed to _getLogoUrl
     *
     * @param string|null $logoFile
     * @return mixed|string
     */
    public function getLogoSrc(?string $logoFile = null)
    {
        return $this->_getLogoUrl($logoFile);
    }

    /**
     * Override the parent block method to get rid of the code dependency on LogoPathResolverInterface
     *
     * The dependency breaks backward compatibility with Magento < 2.4.3
     *
     * @param string|null $logoFile
     * @return string
     * @see \Magento\Theme\Block\Html\Header\Logo::_getLogoUrl
     * @SuppressWarnings(PHPMD.CamelCaseMethodName)
     */
    protected function _getLogoUrl(?string $logoFile = null)
    {
        $path = $logoFile ?: $this->getPath();
        if ($path !== null && $this->_isFile($path)) {
            $url = $this->_urlBuilder->getBaseUrl(['_type' => UrlInterface::URL_TYPE_MEDIA]) . $path;
        } elseif ($logoFile) {
            $url = $this->getViewFileUrl($logoFile);
        } else {
            $url = $this->getViewFileUrl('images/logo.svg');
        }
        return $url;
    }
}