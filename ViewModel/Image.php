<?php

declare(strict_types=1);

namespace CopeX\HyvaTheme\ViewModel;


use Magento\Framework\Filesystem\DirectoryList;
use Magento\Framework\UrlInterface;
use Magento\Framework\Exception\FileSystemException;
use Magento\Framework\Filesystem;
use Magento\Framework\Image\AdapterFactory;
use Magento\Framework\View\Asset\File\NotFoundException;
use Magento\Framework\View\Element\Block\ArgumentInterface;
use Magento\Store\Model\StoreManagerInterface;
use Magento\Framework\Filesystem\Directory\WriteInterface;
use Magento\Framework\Exception\NoSuchEntityException;

class Image implements ArgumentInterface
{
    /**
     * Custom directory relative to the "media" folder
     */
    const DIRECTORY = 'images/resized';


    /**
     * @var WriteInterface
     */
    private $mediaDirectory;

    /**
     * @var AdapterFactory
     */
    private $imageFactory;

    /**
     * @var StoreManagerInterface
     */
    private $storeManager;
    private DirectoryList $directoryList;
    private UrlInterface $urlModel;

    /**
     * @param Filesystem $filesystem
     * @param AdapterFactory $imageFactory
     * @param StoreManagerInterface $storeManager
     */
    public function __construct(
        Filesystem $filesystem,
        AdapterFactory $imageFactory,
        StoreManagerInterface $storeManager,
        DirectoryList $directoryList,
        UrlInterface $urlModel
    ) {
        $this->mediaDirectory = $filesystem->getDirectoryWrite(\Magento\Framework\App\Filesystem\DirectoryList::MEDIA);
        $this->imageFactory = $imageFactory;
        $this->storeManager = $storeManager;
        $this->directoryList = $directoryList;
        $this->urlModel = $urlModel;
    }

    /**
     * Resize image
     *
     * @param string $image
     * @param string|null $width
     * @param string|null $height
     * @return string|null
     * @throws NoSuchEntityException
     */
    public function resize(string $image, $width = null, $height = null, $frame = false, $bg = '#fff'): ?string
    {
        try {
            $path = $this->getPath([$width, $height]);
            $absoluteImagePath = $this->getFilenameFromUrl($image);
            if (strpos($absoluteImagePath, $this->getMediaFolder()) !== false) {
                $imageName = str_replace($this->getMediaFolder(), "", $absoluteImagePath);
            } else {
                $imageName = DIRECTORY_SEPARATOR . basename($image);
            }
            $resizedImagePath = $this->mediaDirectory->getAbsolutePath($path) . $imageName;

            if ($this->fileExists($absoluteImagePath)) {
                if (!$this->fileExists($resizedImagePath)) {
                    $imageFactory = $this->imageFactory->create();
                    $imageFactory->open($absoluteImagePath);
                    $imageFactory->constrainOnly(true);
                    $imageFactory->keepTransparency(true);
                    $imageFactory->keepFrame($frame);
                    if ($bg) {
                        $imageFactory->backgroundColor($bg);
                    }
                    $imageFactory->keepAspectRatio(true);
                    $imageFactory->resize($width ?: null, $height ?: null);
                    $imageFactory->save($resizedImagePath);
                }

                return $this->getMediaUrl() . $path . $imageName;
            }
        }
        catch (\Exception $e) {}
        return $image;
    }


    /**
     * Resize image
     *
     * @param string $image
     * @param string|null $width
     * @param string|null $height
     * @return string|null
     * @throws NoSuchEntityException
     */
    public function crop(string $image, $width = null, $height = null): ?string
    {
        try{
            $path = $this->getPath([$width, $height, "crop"]);
            $absoluteImagePath = $this->getFilenameFromUrl($image);
            $imageName = DIRECTORY_SEPARATOR . basename($image);
            $resizedImagePath = $this->mediaDirectory->getAbsolutePath($path) . $imageName;

            if (!$this->fileExists($absoluteImagePath)) {
                return null;
            }

            if (!$this->fileExists($resizedImagePath)) {
                $imageFactory = $this->imageFactory->create();
                $imageFactory->open($absoluteImagePath);
                $imageFactory->constrainOnly(true);
                $imageFactory->keepTransparency(true);
                $imageFactory->keepFrame(true);
                $imageFactory->keepAspectRatio(true);
                $imageFactory->crop(($imageFactory->getOriginalHeight() - $height) / 2, ($imageFactory->getOriginalWidth() - $width) / 2, ($imageFactory->getOriginalWidth() - $width) / 2, ($imageFactory->getOriginalHeight() - $height) / 2);
                $imageFactory->save($resizedImagePath);
            }

            return $this->getMediaUrl() . $path . $imageName;
        }
        catch (\Exception $e) {}
        return $image;
    }

    /**
     * First check this file on FS
     *
     * @param string $filename
     * @return bool
     */
    private function fileExists(string $filename): bool
    {
        return $this->mediaDirectory->isFile($filename);
    }

    /**
     * @param string|null $width
     * @param string|null $height
     * @return string
     */
    private function getPath($params): string
    {
        return self::DIRECTORY . DIRECTORY_SEPARATOR .  implode("x", $params);
    }

    /**
     * @return string
     */
    private function getMediaUrl(): string
    {
        try {
            return $this->storeManager->getStore()->getBaseUrl(UrlInterface::URL_TYPE_MEDIA);
        } catch (NoSuchEntityException $exception) {
            return '';
        }
    }

    /**
     * @param string $url
     * @return string
     */
    public function getFilenameFromUrl(string $url): string
    {
        $url = preg_replace('/\/static\/version([0-9]+\/)/', '/static/', (string)$url);

        if ($this->isLocal($url) === false) {
            throw new NotFoundException((string)__('URL "' . $url . '" does not appear to be a local file'));
        }

        try {
            if (strpos($url, $this->getMediaUrl()) !== false) {
                return str_replace($this->getMediaUrl(), $this->getMediaFolder() . '/', $url);
            }
        } catch (FileSystemException | NoSuchEntityException $e) {
            throw new NotFoundException((string)__('Media folder does not exist'));
        }

        if (strpos($url, $this->getBaseUrl()) !== false) {
            return str_replace($this->getBaseUrl(), $this->getBaseFolder() . '/', $url);
        }

        if (preg_match('/^\//', $url)) {
            return $this->getBaseFolder() . $url;
        }

        throw new NotFoundException((string)__('URL "' . $url . '" is not matched with a local file'));
    }

    /**
     * @return string
     */
    private function getBaseUrl(): string
    {
        return $this->urlModel->getBaseUrl();
    }

    /**
     * @return string
     */
    private function getBaseFolder(): string
    {
        return $this->directoryList->getRoot() . '/pub';
    }

    /**
     * @return string
     * @throws FileSystemException
     */
    private function getMediaFolder(): string
    {
        return $this->directoryList->getPath('media');
    }

    /**
     * @param string $url
     * @return bool
     */
    public function isLocal(string $url): bool
    {
        if (!preg_match('/^https?:\/\//', $url)) {
            return true;
        }

        if (strpos($url, $this->getBaseUrl()) !== false) {
            return true;
        }

        try {
            if (strpos($url, $this->getMediaUrl()) !== false) {
                return true;
            }
        } catch (NoSuchEntityException $e) {
            return false;
        }

        return false;
    }
}
