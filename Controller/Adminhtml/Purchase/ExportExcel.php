<?php
/**
 * Copyright ©  All rights reserved.
 * See COPYING.txt for license details.
 */
declare(strict_types=1);

namespace Incert\Voucher\Controller\Adminhtml\Purchase;

use Magento\Backend\App\Action;
use Magento\Backend\App\Action\Context;
use Magento\Framework\App\Response\Http\FileFactory;
use Magento\Framework\App\Filesystem\DirectoryList;
use Magento\Ui\Component\MassAction\Filter;
use Incert\Voucher\Model\ResourceModel\Purchase\Grid\CollectionFactory;

class ExportExcel extends Action
{
    const ADMIN_RESOURCE = 'Incert_Voucher::Purchase_view';

    /**
     * @var FileFactory
     */
    protected $fileFactory;

    /**
     * @var Filter
     */
    protected $filter;

    /**
     * @var CollectionFactory
     */
    protected $collectionFactory;

    /**
     * Constructor
     *
     * @param Context $context
     * @param FileFactory $fileFactory
     * @param Filter $filter
     * @param CollectionFactory $collectionFactory
     */
    public function __construct(
        Context $context,
        FileFactory $fileFactory,
        Filter $filter,
        CollectionFactory $collectionFactory
    ) {
        $this->fileFactory = $fileFactory;
        $this->filter = $filter;
        $this->collectionFactory = $collectionFactory;
        parent::__construct($context);
    }

    /**
     * Export action
     *
     * @return \Magento\Framework\App\ResponseInterface
     * @throws \Exception
     */
    public function execute()
    {
        $collection = $this->filter->getCollection($this->collectionFactory->create());
        
        $fileName = 'sold_vouchers_' . date('Y-m-d_H-i-s') . '.xlsx';
        
        $content = $this->getXmlContent($collection);
        
        return $this->fileFactory->create(
            $fileName,
            $content,
            DirectoryList::VAR_DIR,
            'application/vnd.ms-excel'
        );
    }

    /**
     * Generate XML content for Excel
     *
     * @param \Incert\Voucher\Model\ResourceModel\Purchase\Grid\Collection $collection
     * @return string
     */
    protected function getXmlContent($collection)
    {
        $xml = '<?xml version="1.0"?>' . PHP_EOL;
        $xml .= '<Workbook xmlns="urn:schemas-microsoft-com:office:spreadsheet" xmlns:o="urn:schemas-microsoft-com:office:office" xmlns:x="urn:schemas-microsoft-com:office:excel" xmlns:ss="urn:schemas-microsoft-com:office:spreadsheet" xmlns:html="http://www.w3.org/TR/REC-html40">' . PHP_EOL;
        $xml .= '<Worksheet ss:Name="' . __('Sold Vouchers') . '">' . PHP_EOL;
        $xml .= '<Table>' . PHP_EOL;
        
        // Header
        $xml .= '<Row>' . PHP_EOL;
        $headers = [
            __('ID'),
            __('Incert Order ID'),
            __('Invoice'),
            __('Status'),
            __('Date')
        ];
        foreach ($headers as $header) {
            $xml .= '<Cell><Data ss:Type="String">' . __($header) . '</Data></Cell>' . PHP_EOL;
        }
        $xml .= '</Row>' . PHP_EOL;
        
        // Data
        foreach ($collection as $item) {
            $xml .= '<Row>' . PHP_EOL;
            
            $status = $this->getStatusLabel((int)$item->getStatus());
            $date = $item->getCreatedAt() ? date('Y-m-d H:i:s', strtotime($item->getCreatedAt())) : '';
            
            $data = [
                $item->getEntityId(),
                $item->getIncertOrderId(),
                $item->getInvoiceIncrementId() ?: '-',
                $status,
                $date
            ];
            
            foreach ($data as $value) {
                $xml .= '<Cell><Data ss:Type="String">' . $value . '</Data></Cell>' . PHP_EOL;
            }
            
            $xml .= '</Row>' . PHP_EOL;
        }
        
        $xml .= '</Table>' . PHP_EOL;
        $xml .= '</Worksheet>' . PHP_EOL;
        $xml .= '</Workbook>' . PHP_EOL;
        
        return $xml;
    }

    /**
     * Get status label
     *
     * @param int $status
     * @return string
     */
    private function getStatusLabel(int $status): string
    {
        switch ($status) {
            case \Incert\Voucher\Model\Purchase::STATUS_ENABLED:
                return __('Active');
            case \Incert\Voucher\Model\Purchase::STATUS_CANCELED:
                return __('Canceled');
            default:
                return __('Unknown');
        }
    }
}
