<?php
namespace CopeX\VatValidationFrontend\Controller\Index;

use CopeX\VatValidationFrontend\Helper\Data;
use CopeX\VatValidationFrontend\Model\Config\Source\ValidationStatusMessage;
use CopeX\VatValidator\Model\Validator;
use Magento\Customer\Model\Vat;
use Magento\Framework\App\Action\HttpGetActionInterface;
use Magento\Framework\App\RequestInterface;
use Magento\Framework\App\ResponseInterface;
use Magento\Framework\Controller\Result\Json;
use Magento\Framework\Controller\Result\JsonFactory;
use Magento\Framework\Controller\ResultInterface;
use Magento\Framework\Data\Form\FormKey\Validator as FormKeyValidator;

class Validate implements HttpGetActionInterface
{
    /**
     * @var JsonFactory
     */
    protected $resultJsonFactory;

    /**
     * @var Validator
     */
    protected $validator;

    /**
     * @var RequestInterface
     */
    private $request;
    private Vat $customerVat;
    private Data $vatDataHelper;
    private FormKeyValidator $formKeyValidator;

    /**
     * @param RequestInterface $request
     * @param JsonFactory $resultJsonFactory
     * @param Validator $validator
     * @param Data $vatDataHelper
     * @param FormKeyValidator $formKeyValidator
     */
    public function __construct(
        RequestInterface $request,
        JsonFactory $resultJsonFactory,
        Validator $validator,
        Vat $customerVat,
        Data $vatDataHelper,
        FormKeyValidator $formKeyValidator
    ) {
        $this->request = $request;
        $this->resultJsonFactory = $resultJsonFactory;
        $this->validator = $validator;
        $this->customerVat = $customerVat;
        $this->vatDataHelper = $vatDataHelper;
        $this->formKeyValidator = $formKeyValidator;
    }

    /**
     * @return ResponseInterface|Json|ResultInterface
     */
    public function execute(): ResultInterface
    {
        $response = false;
        $result = $this->resultJsonFactory->create();

        if (!$this->formKeyValidator->validate($this->request)) {
            return $result->setData([
                'success' => false,
                'message' => __('Invalid form key.'),
            ]);
        }

        if (!$this->vatDataHelper->isFrontendEnabled()) {
            return $result->setData([
                'live_validation_status_enabled' => false,
            ]);
        }

        $vatId = $this->request->getParam('vat_id');

        if ($vatId && $this->request->isAjax()) {
            $this->validator->setCheckQualified($this->vatDataHelper->qualifiedValidationInFrontend());
            $response = $this->validator->checkVatNumber(
                substr($vatId,0,2),
                $vatId,
                $this->customerVat->getMerchantCountryCode(),
                $this->customerVat->getMerchantVatNumber(),
                $this->request->getParam('company'),
                $this->request->getParam('postcode'),
                $this->request->getParam('city'),
                $this->request->getParam('street')
            );
        }
        return $this->prepareResult($result, $response);
    }

    private function getMessage($response)
    {
        $message = "";
       foreach( $response['response']['qualified']['error'] ?? [] as $error) {
           $message .= __($error) . "\n";
       }
       return $message ?: ($response['message'] ?? "");
    }

    private function prepareResult($result, $response){
        $message = $this->getMessage($response);
        return $result->setData([
            'response' => $response,
            'is_valid' => $response['is_valid'] ?? false,
            'is_qualified' => $response['is_qualified'] ?? false,
            'vat_valid' => $response['vat_valid'] ?? false,
            'message' => $message,
            'live_validation_status_enabled' => true,
        ]);
    }
}
