
document.addEventListener("DOMContentLoaded", function () {
    if (window.VAT_VALIDATE_FRONTEND_ENABLED) {
        let vatField, company,  city, postcode, streets, vatMessage, lastVatRequestNumber = "";
        let formKey = "";
        const vatMessageId = "vat-message", retryButtonId = 'vat-retry-button';
        const iconValid = '<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor" class="size-6"><path fill-rule="evenodd" d="M2.25 12c0-5.385 4.365-9.75 9.75-9.75s9.75 4.365 9.75 9.75-4.365 9.75-9.75 9.75S2.25 17.385 2.25 12Zm13.36-1.814a.75.75 0 1 0-1.22-.872l-3.236 4.53L9.53 12.22a.75.75 0 0 0-1.06 1.06l2.25 2.25a.75.75 0 0 0 1.14-.094l3.75-5.25Z" clip-rule="evenodd" /></svg>';
        const iconInvalid = '<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor" className="size-6"><path fill-rule="evenodd" d="M12 2.25c-5.385 0-9.75 4.365-9.75 9.75s4.365 9.75 9.75 9.75 9.75-4.365 9.75-9.75S17.385 2.25 12 2.25Zm-1.72 6.97a.75.75 0 1 0-1.06 1.06L10.94 12l-1.72 1.72a.75.75 0 1 0 1.06 1.06L12 13.06l1.72 1.72a.75.75 0 1 0 1.06-1.06L13.06 12l1.72-1.72a.75.75 0 1 0-1.06-1.06L12 10.94l-1.72-1.72Z" clip-rule="evenodd"/> </svg>';
        const iconQualified = '<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor" class="size-6"><path fill-rule="evenodd" d="M12.516 2.17a.75.75 0 0 0-1.032 0 11.209 11.209 0 0 1-7.877 ********** 0 0 0-.722.515A12.74 12.74 0 0 0 2.25 9.75c0 5.942 4.064 10.933 9.563 12.348a.749.749 0 0 0 .374 0c5.499-1.415 9.563-6.406 9.563-12.348 0-1.39-.223-2.73-.635-3.985a.75.75 0 0 0-.722-.516l-.143.001c-2.996 0-5.717-1.17-7.734-3.08Zm3.094 8.016a.75.75 0 1 0-1.22-.872l-3.236 4.53L9.53 12.22a.75.75 0 0 0-1.06 1.06l2.25 2.25a.75.75 0 0 0 1.14-.094l3.75-5.25Z" clip-rule="evenodd" /></svg>';
        const waitForVatField = () => {
            vatField = document.querySelector('input[name="vat_id"], input[name="taxvat"]');
            if (vatField) {
                formKey = getFormKeyFromCookie();
                company = document.querySelector('input[name="company"]');
                city = document.querySelector('input[name="city"]');
                postcode = document.querySelector('input[name="postcode"]');
                streets = document.querySelectorAll('input[name^="street["]');
                clearInterval(vatFieldInterval);
                if (!document.getElementById(vatMessageId)) {
                    vatMessage = document.createElement("div");
                    vatMessage.id = vatMessageId;
                    vatField.parentNode.insertBefore(vatMessage, vatField.nextSibling);
                }

                let typingTimer;
                const typingDelay = window.VAT_VALIDATION_INPUT_DELAY;

                vatField.addEventListener('input', function () {
                    clearTimeout(typingTimer);
                    if (vatField.value) {
                        typingTimer = setTimeout(() => validateVAT(vatField.value), typingDelay);
                    } else {
                        vatMessage.textContent = '';
                    }
                });

            }
        };

        const validateVAT = (vatNumber) => {
            if(lastVatRequestNumber === vatNumber) return;
            const url = window.VAT_VALIDATION_URL;
            let params = {
                vat_id: vatNumber,
                form_key: formKey,
                company: company.value,
                city: city.value,
                postcode: postcode.value,
                street: Array.from(streets)
                    .map(input => input.value.trim())
                    .filter(value => value !== '').join(' ')
                };
            const queryParams = new URLSearchParams(params).toString();

            const fullUrl = `${url}?${queryParams}`;

            fetch(fullUrl, {
                method: 'GET',
                headers: {
                    'Content-Type': 'application/json',
                    'X-Requested-With': 'XMLHttpRequest'
                },
            })
                .then((response) => response.json())
                .then((data) => {
                    document.getElementById(retryButtonId)?.classList.remove("spin");
                    if (data.live_validation_status_enabled) {
                        vatMessage.innerHTML = data.message?.replaceAll("\n","<br/>");
                        vatMessage.style.color = data.is_valid ? 'green' : 'red';
                        if(!data.is_valid){
                            vatMessage.innerHTML =  iconInvalid + "<div>" + vatMessage.innerHTML + "</div>";
                            addRetryButton();
                        } else {
                            vatMessage.innerHTML =  ( data.is_qualified ? iconQualified : iconValid ) + vatMessage.innerHTML;
                            removeRetryButton();
                        }
                    }
                })
                .catch((e) => {
                    vatMessage.textContent = 'Error validating VAT.';
                    vatMessage.style.color = 'red';
                });
        };

        const addRetryButton = () => {
            const retryButton = document.createElement("button");
            retryButton.id = retryButtonId;
            retryButton.style.marginLeft = "8px";
            retryButton.style.border = "none";
            retryButton.style.background = "transparent";
            retryButton.style.cursor = "pointer";
            retryButton.style.fontSize = "16px";
            retryButton.style.transition = "transform 0.5s ease-in-out";
            retryButton.innerHTML = "&#x21bb;";
            retryButton.onclick = function(e) {
                e.preventDefault();
                retryButton.classList.add("spin");
                validateVAT(vatField.value);
            };

            vatMessage.appendChild(retryButton)
        }

        const removeRetryButton = ()  => {
            document.getElementById(retryButtonId)?.remove();
        }

        const getFormKeyFromCookie = () => {
            const match = document.cookie.match(/form_key=([^;]+)/);
            return match ? match[1] : getFormKeyFromInput();
        };

        const getFormKeyFromInput = () => {
            const formKeyInput = document.querySelector('input[name="form_key"]');
            return formKeyInput ? formKeyInput.value : null;
        };

        const vatFieldInterval = setInterval(waitForVatField, 100);
    }
});
