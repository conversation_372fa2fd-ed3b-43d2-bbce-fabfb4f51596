define([
    'jquery'
], function ($) {
    'use strict';

    return function (target) {
        return target.extend({
            /**
             * Override to only show coupon when it's not set in totals
             * @return {Boolean}
             */
            haveToShowCoupon: function () {
                let orig = this._super();
                if(orig && this.totals()
                    && this.totals()['base_subtotal'] == this.totals()['base_subtotal_with_discount']
                    && this.totals()['total_segments'].find(segment => segment.code === 'voucher')?.value < 0){
                    orig = false;
                }
                return orig;
            }
        });
    };
});