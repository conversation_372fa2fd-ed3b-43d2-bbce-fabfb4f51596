define([
    'mage/utils/wrapper',
    'mage/translate',
    'Magento_Ui/js/modal/alert'
], function (wrapper, $t, alert) {
    'use strict';

    return function (target) {
        target.process = wrapper.wrapSuper(target.process, function(response, messageContainer){
            const error = JSON.parse(response.responseText);
            if (response.responseJSON.hasOwnProperty('trace') && error.message && response.responseJSON.trace.includes('VatValidationFrontend')) {
                alert({title: $t('Error'), content: error.message});
            }
            else {
                this._super(response, messageContainer);
            }
        });

        return target;
    };
});
