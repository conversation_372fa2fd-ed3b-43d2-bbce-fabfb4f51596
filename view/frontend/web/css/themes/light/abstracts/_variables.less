//
// Body variables
// ______________________________________________
@fc__accent-color: #00893c;

//
// Loader
// ______________________________________________
@fc-loader__primary-color: @fc__accent-color;

//
// Section variables
// ______________________________________________
@fc-section-desktop__gap: 27px;
@fc-section__gap: 10px;
@fc-section__padding: 25px;
@fc-section__background: #fff;
@fc-section__border-color: #f2f5f7;
@fc-section__border-width: 1px;
@fc-section__border: @fc-section__border-width solid @fc-section__border-color;
@fc-section__border-radius: 4px;
@fc-section__box-shadow: 0 10px 20px -5px rgba(0, 0, 0, 0.05);

@fc-section-title__font-weight: 500;
@fc-section-title__padding: 0 0 15px;

@fc-section-number__width: 32px;
@fc-section-number__height: @fc-section-number__width;
@fc-section-number__color: @fc__accent-color;
@fc-section-number__font-family: inherit;
@fc-section-number__background: transparent;
@fc-section-number__font-size: 20px;
@fc-section-number__font-weight: 600;
@fc-section-number__margin: 0 10px 0 -9px;
@fc-section-number__border-width: 2px;
@fc-section-number__border-color: @fc__accent-color;
@fc-section-number__border-radius: 50%;
@fc-section-number__border: @fc-section-number__border-width solid @fc-section-number__border-color;

@fc-subtitle__color: #737586;

@fc-toggler__color: #737586;
@fc-toggler__font-size: 12px;

//
// Form variables
// ______________________________________________
@fc-form-field__background-color: #f2f5f7;
@fc-form-field__border-color: @fc-form-field__background-color;
@fc-form-field__border-width: 2px;
@fc-form-field__border-radius: 3px;
@fc-form-field__height: 50px;
@fc-form-field__gap: 6px;
@fc-form-field__focus__border-color: shade(@fc-form-field__border-color, 7%);
@fc-form-field__error__border-color: #fe6b22;
@fc-form-field__focus__background-color: lighten(@fc-form-field__background-color, 5%);

@fc-form-checkbox-radio__background-color: @fc-section__background;
@fc-form-checkbox-radio__border-color: #ddd;
@fc-form-checkbox-radio__box-shadow-color: lighten(@fc-form-checkbox-radio__border-color, 10%);
@fc-form-checkbox-radio__checked__box-shadow-color: darken(@fc-form-checkbox-radio__box-shadow-color, 25%);
@fc-form-checkbox-radio__focus__border-color: darken(@fc-form-checkbox-radio__border-color, 15%);

//
// Buttons
// ______________________________________________
@fc-button__background: #fff;
@fc-button__border-color: @fc__accent-color;
@fc-button__border-width: 2px;
@fc-button__border-radius: 3px;
@fc-button__box-shadow: none;
@fc-button__text-shadow: none;
@fc-button__color: @fc__accent-color;
@fc-button__padding: 7px 15px;
@fc-button__text-decoration: none;
@fc-button__transition: all 200ms ease-in-out;
@fc-button__hover__background: @fc__accent-color;
@fc-button__hover__color: #fff;

@fc-button-secondary__background: #fff;
@fc-button-secondary__border-color: transparent;
@fc-button-secondary__color: #777;
@fc-button-secondary__padding: 0;

@fc-button-primary__background: @fc__accent-color;
@fc-button-primary__border-color: @fc-button-primary__background;
@fc-button-primary__color: #fff;
@fc-button-primary__padding: 13px 25px;
@fc-button-primary__hover__border-color: shade(@fc-button-primary__border-color, 10%);
@fc-button-primary__hover__background: shade(@fc-button-primary__background, 10%);
@fc-button-primary__hover__color: @fc-button-primary__color;

@fc-button-order__stick-to-bottom: true;
@fc-button-order__background: @fc__accent-color;
@fc-button-order__color: #fff;
@fc-button-order__padding: 18px 15px;
@fc-button-order__border-color: @fc-button-order__background;
@fc-button-order__border-radius: 4px;
@fc-button-order__hover__border-color: shade(@fc-button-order__border-color, 10%);
@fc-button-order__hover__background: shade(@fc-button-order__background, 10%);
@fc-button-order__hover__color: @fc-button-order__color;

@fc-button-auth__enabled: true;
@fc-button-auth__background: @fc__accent-color;
@fc-button-auth__border-color: transparent;
@fc-button-auth__color: #fff;
@fc-button-auth__padding: 5px 12px;
@fc-button-auth__hover__background: shade(@fc-button-auth__background, 10%);

//
// Modal and authentication popup
// ______________________________________________
@fc-modal__border-radius: @fc-section__border-radius;

//
// Saved addresses variables
// ______________________________________________
@fc-address-item__border-color: @fc__accent-color;
