//
// Body variables
// ______________________________________________
@fc__accent-color: #2999eb;
@fc__accent-dark-color: #5494f9;

@fc-body__background-color: #eff2f7;

//
// Loader
// ______________________________________________
@fc-loader__primary-color: @fc__accent-color;

//
// Section variables
// ______________________________________________
@fc-section-desktop__gap: 27px;
@fc-section__padding: 25px;
@fc-section__border: 0;
@fc-section__border-radius: 10px;
@fc-section__box-shadow: 0 3px 23px -3px rgba(0, 0, 0, 0.1);

@fc-section-title__font-weight: 500;
@fc-section-title__color: @fc__accent-color;
@fc-section-title__padding: 0 0 15px;

@fc-section-number__width: 32px;
@fc-section-number__height: @fc-section-number__width;
@fc-section-number__font-family: inherit;
@fc-section-number__background: linear-gradient(90deg, @fc__accent-color 0%, @fc__accent-dark-color 100%);
@fc-section-number__font-size: 20px;
@fc-section-number__font-weight: 600;
@fc-section-number__margin: 0 10px 0 -7px;
@fc-section-number__border-radius: 50%;
@fc-section-number__border: 0;

@fc-subtitle__color: #737586;

@fc-toggler__color: @fc__accent-color;
@fc-toggler__font-size: 12px;

//
// Form variables
// ______________________________________________
@fc-form-field__background-color: transparent;
@fc-form-field__border-color: #eff2f7;
@fc-form-field__border-width: 2px;
@fc-form-field__border-radius: 30px;
@fc-form-field__height: 50px;
@fc-form-field__padding-left: 18px;
@fc-form-field__focus__border-color: shade(@fc-form-field__border-color, 7%);
@fc-form-field__error__border-color: #fe545d;
@fc-form-field__focus__background-color: transparent;

@fc-form-checkbox-radio__border-color: #e7e9ef;
@fc-form-checkbox-radio__border-width: 2px;
@fc-form-checkbox-radio__background-color: @fc-section__background;
@fc-form-checkbox-radio__box-shadow-color: lighten(@fc-form-checkbox-radio__border-color, 10%);
@fc-form-checkbox-radio__checked__box-shadow-color: darken(@fc-form-checkbox-radio__box-shadow-color, 25%);
@fc-form-checkbox-radio__focus__border-color: darken(@fc-form-checkbox-radio__border-color, 15%);

@fc-form-field-icon__right: 10px;

//
// Messages
// ______________________________________________
@fc-message__border-radius: @fc-form-field__border-radius;

//
// Buttons
// ______________________________________________
@fc-button__text-decoration: none;
@fc-button__text-shadow: none;
@fc-button__border-radius: 40px;
@fc-button__border-width: 1px;
@fc-button__border-color: transparent;
@fc-button__box-shadow: 0 7px 30px -5px rgba(0, 0, 0, 0.25);
@fc-button__background: linear-gradient(90deg, @fc__accent-color 0%, @fc__accent-dark-color 100%);
@fc-button__color: #fff;
@fc-button__padding: 7px 15px;
@fc-button__transition: all 200ms ease-in-out;
@fc-button__hover__box-shadow: 0 4px 20px -5px rgba(0, 0, 0, 0.25);

@fc-button-secondary__background: #fff;
@fc-button-secondary__box-shadow: none;
@fc-button-secondary__border-width: 0;
@fc-button-secondary__color: #777;
@fc-button-secondary__padding: 0;

@fc-button-primary__padding: 13px 25px;
@fc-button-primary__hover__box-shadow: @fc-button__hover__box-shadow;

@fc-button-order__wide-layout: true;
@fc-button-order__background: linear-gradient(90deg, @fc__accent-color 0%, @fc__accent-dark-color 100%);
@fc-button-order__color: #fff;
@fc-button-order__padding: 18px 35px;
@fc-button-order__border-radius: 30px;
@fc-button-order__hover__box-shadow: @fc-button__hover__box-shadow;

@fc-button-auth__enabled: true;
@fc-button-auth__hover__box-shadow: @fc-button__hover__box-shadow;

//
// Modal and authentication popup
// ______________________________________________
@fc-modal__border-radius: 10px;

//
// Saved addresses variables
// ______________________________________________
@fc-address-item__border-radius: 10px;
@fc-address-item__border-color: @fc__accent-color;

//
// Discount
// ______________________________________________
@fc-discount__overlap-button: true;

//
// Order summary
// ______________________________________________
@fc-order-totals__border-radius: 7px;
@fc-order-totals__border-collapse: separate;
@fc-order-totals__border: 0;
@fc-order-totals__background: #f6f9fc;
@fc-order-totals__padding: 7px 15px;
@fc-order-totals__margin: 15px 0 25px;

//
// Third-party variables
// ______________________________________________
// @fc-orderattachments__border: false;

@fc-delivery-date-icon__margin-left: -35px;

@fc-checkout-cart__qty-wrapper-border-radius: 10px;
@fc-checkout-cart__toggler-color: #999;
