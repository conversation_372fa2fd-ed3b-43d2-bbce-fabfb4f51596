.firecheckout {
    // <PERSON><PERSON> resets to make popup looks the same as on desktop
    .modal-popup,
    .modal-custom:not(.opc-sidebar) {
        &.modal-slide {
            &._inner-scroll {
                &._show {
                    overflow-y: visible;
                }
                .modal-inner-wrap {
                    min-height: 0;
                    &[class] {
                        background: @fc-modal__popup-background;
                    }
                }
            }
        }
    }
    .modal-unclosable {
        .action-close {
            display: none;
        }
    }
    .modal-popup,
    .modal-custom:not(.opc-sidebar) {
        .step-title {
            font-size: 25px;
        }
    }

    // hide loading mask to prevent background flickering behind popup
    &._has-modal,
    &._has-modal-custom {
        > .loading-mask {
            z-index: 10005 !important;
        }
        .loading-mask:not(.fc-mask) {
            opacity: 0;
            pointer-events: none;
        }
        &._has-auth-shown {
            .loading-mask:not(.fc-mask) {
                opacity: 1;
                pointer-events: all;
            }
        }
    }
}

.firecheckout {
    .modal-popup,
    .modal-custom:not(.opc-sidebar) {
        left: 0;
        right: 0;
        bottom: 0;
        top: 0;
        position: fixed;
        visibility: hidden;
        opacity: 0;
        -webkit-transition: visibility 0s .3s,
                            opacity .3s ease;
        transition: visibility 0s .3s,
                    opacity .3s ease;

        border: 0;
        width: 100%;
        max-width: none;
        box-shadow: none;
        background: transparent;
        .text-align(left);
        transform: none;

        .modal-title {
            .lib-fc-css(border-color, @fc__divider-color);
        }
        .modal-inner-wrap {
            margin: 3rem auto;
            width: 90%;
            max-width: 800px;
            min-height: 0;
            max-height: ~"calc(90vh - 6rem)";
            background: @fc-modal__popup-background;
            box-shadow: @fc-modal__popup-box-shadow;
            transform-origin: 50% 50% 0;
            opacity: 0;
            transition: all 0.2s;
            visibility: hidden;
            .lib-fc-css(border-radius, @fc-modal__border-radius);

            & when (@fc-modal__popup-effect = 'zoom') {
                transform: scale(0.95);
            }
            & when (@fc-modal__popup-effect = 'slide-down') {
                transform: translateY(-20px);
            }
            & when (@fc-modal__popup-effect = 'slide-up') {
                transform: translateY(20px);
            }

            .modal-content {
                .lib-fc-scrollbar();
            }
        }
        ~ .modals-overlay {
            background: @fc-modal__overlay-background;
            transition: opacity 0.2s;
            opacity: 0;
        }

        &._show {
            visibility: visible;
            opacity: 1;
            -webkit-transition: opacity .3s ease;
            transition: opacity .3s ease;

            .modal-inner-wrap {
                opacity: 1;
                visibility: visible;
                top: 0;

                & when (@fc-modal__popup-effect = 'zoom') {
                    transform: scale(1);
                }
                & when (@fc-modal__popup-effect = 'slide-down') {
                    transform: translateY(0);
                }
                & when (@fc-modal__popup-effect = 'slide-up') {
                    transform: translateY(0);
                }
            }

            ~ .modals-overlay {
                opacity: 1;
            }
        }

        &.modal-slide .modal-footer,
        .modal-footer {
            .lib-fc-css(border-color, @fc__divider-color);
        }
    }

    .modal-inner-wrap {
        .action-close {
            width: 30px;
            height: 30px;
            margin: 0;
            padding: 0;

            &,
            &:focus,
            &:hover {
                background: transparent;
            }

            &::before,
            &::after {
                -webkit-mask: none;
                content: '';
                position: absolute;
                .left(11px);
                top: 10px;
                height: 20px;
                width: 2px;
                margin: 0;
                background-color: #333;
                opacity: 1;
                .lib-fc-css(background-color, @fc__text-color);
                .lib-fc-css(color, @fc__text-color);
            }
            &::before {
                transform: rotate(45deg);
            }
            &::after {
                transform: rotate(-45deg);
            }
            & when not (@fc__text-color = false) {
                &:hover {
                    &::before,
                    &::after {
                        .lib-fc-css(color, fadeout(@fc__text-color, 30%));
                        .lib-fc-css(background-color, fadeout(@fc__text-color, 30%));
                    }
                }
            }
        }
    }
}

@media @fc-screen__base__media {
    .firecheckout {
        &._has-modal {
            overflow-y: auto;
        }
        &.checkout-index-index {
            .modal-popup,
            .modal-custom:not(.opc-sidebar) {
                .modal-inner-wrap {
                    left: 0;
                    margin: 5rem auto;
                    width: 75%;
                }
            }
        }
    }
}
