//
// Third-party themes compatibility
// Use ID's to reset numerous bugs with third-party themes
// ______________________________________________
@fc-container__selector: ~"#checkout";
@fc-wrapper__selector: ~"#checkout .opc-wrapper";

//
// Responsive breakpoints
//
// `small` - mobile size
// `base`  - firecheckout layout will be applied
// `large` - additional spacing may be added between boxes
// ______________________________________________
@fc-screen__small__max-width: 639px;
@fc-screen__base__min-width: 768px;
@fc-screen__large__min-width: 1024px;

@fc-screen__small__media: ~"(max-width: @{fc-screen__small__max-width})";
@fc-screen__base__media: ~"(min-width: @{fc-screen__base__min-width})";
@fc-screen__large__media: ~"(min-width: @{fc-screen__large__min-width})";

//
// Icons
// ______________________________________________
@fc-icon__error: 'data:image/svg+xml;base64,PHN2ZyBpZD0iaS1hbGVydCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIiB2aWV3Qm94PSIwIDAgMzIgMzIiIHdpZHRoPSIzMiIgaGVpZ2h0PSIzMiIgZmlsbD0ibm9uZSIgc3Ryb2tlPSIjZTgwNTAwIiBzdHJva2UtbGluZWNhcD0iYnV0dCIgc3Ryb2tlLWxpbmVqb2luPSJtaXRlciIgc3Ryb2tlLXdpZHRoPSIxIj4KICAgIDxwYXRoIGQ9Ik0xNiAzIEwzMCAyOSAyIDI5IFogTTE2IDExIEwxNiAxOSBNMTYgMjMgTDE2IDI1IiAvPgo8L3N2Zz4=';
@fc-icon__warning: 'data:image/svg+xml;base64,PHN2ZyBpZD0iaS1hbGVydCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIiB2aWV3Qm94PSIwIDAgMzIgMzIiIHdpZHRoPSIzMiIgaGVpZ2h0PSIzMiIgZmlsbD0ibm9uZSIgc3Ryb2tlPSIjZmZhYzAwIiBzdHJva2UtbGluZWNhcD0iYnV0dCIgc3Ryb2tlLWxpbmVqb2luPSJtaXRlciIgc3Ryb2tlLXdpZHRoPSIxIj4KICAgIDxwYXRoIGQ9Ik0xNiAzIEwzMCAyOSAyIDI5IFogTTE2IDExIEwxNiAxOSBNMTYgMjMgTDE2IDI1IiAvPgo8L3N2Zz4=';
@fc-icon__info: 'data:image/svg+xml;base64,PHN2ZyBpZD0iaS1pbmZvIiB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHZpZXdCb3g9IjAgMCAzMiAzMiIgd2lkdGg9IjMyIiBoZWlnaHQ9IjMyIiBmaWxsPSJub25lIiBzdHJva2U9IiM1NTUiIHN0cm9rZS1saW5lY2FwPSJidXR0IiBzdHJva2UtbGluZWpvaW49Im1pdGVyIiBzdHJva2Utd2lkdGg9IjEiPgogICAgPHBhdGggZD0iTTE2IDE0IEwxNiAyMyBNMTYgOCBMMTYgMTAiIC8+CiAgICA8Y2lyY2xlIGN4PSIxNiIgY3k9IjE2IiByPSIxNCIgLz4KPC9zdmc+';
@fc-icon__caret: 'data:image/svg+xml;base64,********************************************************************************************************************************************************************************************************************************************************************************************************************************************************';

//
// Page variables
// ______________________________________________
@fc-page-wide__width: 1280px;
@fc-page-narrow__width: 1080px;

//
// Layout variables
// ______________________________________________
@fc-layout-1column__content-width: 63%;
@fc-layout-1column__sidebar-width: 100% - @fc-layout-1column__content-width;

@fc-layout-2columns__column1-width: 40%;
@fc-layout-2columns__column2-width: 100% - @fc-layout-2columns__column1-width;

@fc-layout-2columns-alt__column1-width: (100% / 3);
@fc-layout-2columns-alt__column2-width: 100% - @fc-layout-2columns-alt__column1-width;
@fc-layout-2columns-alt__column2-narrow-width: @fc-layout-2columns-alt__column2-width / 2;

@fc-layout-3columns__columns-width: 35%;
@fc-layout-3columns__column3-width: 100% - (@fc-layout-3columns__columns-width * 2);

//
// Body variables
// ______________________________________________
@fc__accent-color: false;
@fc__text-color: false;
@fc__text-secondary-color: false;
@fc__action-color: false;
@fc__divider-color: #f4f4f4;

@fc-body__background-color: false;
@fc-body__background: false;

//
// Loader
// ______________________________________________
@fc-loader-mask__background: false;
@fc-loader__enabled: true;
@fc-loader__width: 30px;
@fc-loader__height: @fc-loader__width;
@fc-loader__border-width: 3px;
@fc-loader__primary-color: #42a5f5;
@fc-loader__secondary-color: #ddd;

//
// Section variables
// ______________________________________________
@fc-section-desktop__gap: 15px;
@fc-section__gap: 10px;
@fc-section__padding: 15px;
@fc-section__background: #fff;
@fc-section__border-color: darken(@fc-section__background, 10%);
@fc-section__border: 1px solid @fc-section__border-color;
@fc-section__border-radius: 0;
@fc-section__box-shadow: none;

@fc-section-title__color: false;
@fc-section-title__font-size: 25px;
@fc-section-title__font-weight: 300;
@fc-section-title__padding: 0 0 10px;

@fc-section-number__width: 23px;
@fc-section-number__height: @fc-section-number__width;
@fc-section-number__color: #fff;
@fc-section-number__background: #000;
@fc-section-number__font-family: Verdana, sans-serif;
@fc-section-number__font-size: 15px;
@fc-section-number__font-weight: normal;
@fc-section-number__margin: 0 7px 0 0;
@fc-section-number__border-radius: false;
@fc-section-number__border: false;

@fc-subtitle__color: #444;
@fc-subtitle__text-transform: uppercase;
@fc-subtitle__text-decoration: none;
@fc-subtitle__font-size: 13px;
@fc-subtitle__font-weight: bold;

@fc-toggler__color: false;
@fc-toggler__font-size: false;

//
// Form variables
// ______________________________________________
@fc-form__letter-spacing-fix: -0.4em;

@fc-form-compact__field-width: 50%;
@fc-form-compact__l__field-width: 100% / 3;
@fc-form-compact__xs__field-width: 100%;

@fc-form-field__color: false;
@fc-form-field__placeholder-color: #676767;
@fc-form-field__background-color: #fff;
@fc-form-field-option__background: false;
@fc-form-field__border-color: #ccc;
@fc-form-field__border-width: 1px;
@fc-form-field__border-radius: 0;
@fc-form-field__height: 42px;
@fc-form-field__padding-top: 0.68em;
@fc-form-field__padding-bottom: 0.68em;
@fc-form-field__padding-right: 7px;
@fc-form-field__padding-left: 7px;
@fc-form-field__gap: 4px;
@fc-form-field__focus__border-color: shade(@fc-form-field__border-color, 5%);
@fc-form-field__focus__background-color: shade(@fc-form-field__background-color, 2%);
@fc-form-field__error__border-color: false;
@fc-form-field__autofill__background-color: #ffffe4;
@fc-form-field__autofill__color: false;
@fc-form-field__dirty__padding-top: 1em;
@fc-form-field__dirty__padding-bottom: 0.36em;

@fc-form-field-note__color: #767676;

@fc-form-field-caret__width: 25px;

@fc-form-field-icon__width: 17px;
@fc-form-field-icon__height: @fc-form-field-icon__width;
@fc-form-field-icon__top: round( (@fc-form-field__height / 2) - (@fc-form-field-icon__height / 2));
@fc-form-field-icon__right: @fc-form-field__padding-right;
@fc-form-field-icon__second__right: @fc-form-field__padding-right + @fc-form-field-icon__width + 3px;
@fc-form-field-icon__third__right: @fc-form-field__padding-right + (@fc-form-field-icon__width + 3px) * 2;

@fc-form-checkbox__background-image: 'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABgAAAAUCAQAAAA9vDUIAAAAdklEQVQ4y7XR0QmAIBCAYXOTxmiRfAtnaJ0gcLT2MP5eQjoJvQvy1f8DvXPun8OEt+SBTFITAhlASUoOEG35xvA1Z2a05AsnR03aOSBJ4+3s5aKQ5lfxJEm6k6nIqhikIKjm/kJ6a6pIf6uCaPIH0eY3iYZccy6u+Rcjm6L8iwAAAABJRU5ErkJggg==';
@fc-form-radio__background-image: 'data:image/svg+xml;base64,PD94bWwgdmVyc2lvbj0iMS4wIiBlbmNvZGluZz0idXRmLTgiPz4NCjwhLS0gR2VuZXJhdG9yOiBBZG9iZSBJbGx1c3RyYXRvciAxNi4yLjEsIFNWRyBFeHBvcnQgUGx1Zy1JbiAuIFNWRyBWZXJzaW9uOiA2LjAwIEJ1aWxkIDApICAtLT4NCjwhRE9DVFlQRSBzdmcgUFVCTElDICItLy9XM0MvL0RURCBTVkcgMS4xLy9FTiIgImh0dHA6Ly93d3cudzMub3JnL0dyYXBoaWNzL1NWRy8xLjEvRFREL3N2ZzExLmR0ZCI+DQo8c3ZnIHZlcnNpb249IjEuMSIgaWQ9IkxheWVyXzEiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyIgeG1sbnM6eGxpbms9Imh0dHA6Ly93d3cudzMub3JnLzE5OTkveGxpbmsiIHg9IjBweCIgeT0iMHB4Ig0KCSB3aWR0aD0iNTEycHgiIGhlaWdodD0iNTEycHgiIHZpZXdCb3g9IjAgMCA1MTIgNTEyIiBzdHlsZT0iZW5hYmxlLWJhY2tncm91bmQ6bmV3IDAgMCA1MTIgNTEyOyIgeG1sOnNwYWNlPSJwcmVzZXJ2ZSI+DQo8cGF0aCBkPSJNMjU2LDQ2NGMxMTQuOSwwLDIwOC05My4xLDIwOC0yMDhjMC0xMTQuOS05My4xLTIwOC0yMDgtMjA4QzE0MS4xLDQ4LDQ4LDE0MS4xLDQ4LDI1NkM0OCwzNzAuOSwxNDEuMSw0NjQsMjU2LDQ2NHoiIHN0eWxlPSJmaWxsOiNmZmYiLz4NCjwvc3ZnPg0K';
@fc-form-checkbox-radio__enabled: true;
@fc-form-checkbox-radio__border-width: 1px;
@fc-form-checkbox-radio__border-color: @fc-form-field__border-color;
@fc-form-checkbox-radio__border: @fc-form-checkbox-radio__border-width solid @fc-form-checkbox-radio__border-color;
@fc-form-checkbox-radio__background-color: @fc-section__background;
@fc-form-checkbox-radio__box-shadow-color: lighten(@fc-form-checkbox-radio__border-color, 10%);
@fc-form-checkbox-radio__box-shadow: 0 0 0 0 @fc-form-checkbox-radio__box-shadow-color inset;
@fc-form-checkbox-radio__checked__box-shadow-color: darken(@fc-form-checkbox-radio__box-shadow-color, 25%);
@fc-form-checkbox-radio__checked__box-shadow: 0 0 0 10px @fc-form-checkbox-radio__checked__box-shadow-color inset;
@fc-form-checkbox-radio__checked-focus__box-shadow: 0 0 0 10px darken(@fc-form-checkbox-radio__checked__box-shadow-color, 8%) inset;
@fc-form-checkbox-radio__focus__border-color: darken(@fc-form-checkbox-radio__border-color, 15%);

//
// Spinner
// ______________________________________________
@fc-form-spinner__top: @fc-form-field__height / 2;
@fc-form-spinner__right: @fc-form-field__padding-right;
@fc-form-spinner__size: 10px;
@fc-form-spinner__border-width: 1px;
@fc-form-spinner__secondary-color: @fc-loader__secondary-color;
@fc-form-spinner__primary-color: @fc-loader__primary-color;

//
// Messages
// ______________________________________________
@fc-message__opacity: false;
@fc-message__border-radius: false;
@fc-message__background: false;
@fc-message__success__color: false;
@fc-message__error__color: false;
@fc-message__notice__color: false;

//
// Buttons
// ______________________________________________
@fc-button__color: false;
@fc-button__font-weight: 500;
@fc-button__text-shadow: false;
@fc-button__text-decoration: false;
@fc-button__border-width: false;
@fc-button__border-radius: false;
@fc-button__border-color: false;
@fc-button__box-shadow: false;
@fc-button__background: false;
@fc-button__padding: false;
@fc-button__transition: false;
@fc-button__hover__color: @fc-button__color;
@fc-button__hover__text-shadow: @fc-button__text-shadow;
@fc-button__hover__text-decoration: @fc-button__text-decoration;
@fc-button__hover__border-width: @fc-button__border-width;
@fc-button__hover__border-radius: @fc-button__border-radius;
@fc-button__hover__border-color: @fc-button__border-color;
@fc-button__hover__box-shadow: @fc-button__box-shadow;
@fc-button__hover__background: @fc-button__background;
@fc-button__hover__padding: @fc-button__padding;

@fc-button-secondary__color: false;
@fc-button-secondary__text-shadow: false;
@fc-button-secondary__text-decoration: false;
@fc-button-secondary__border-width: false;
@fc-button-secondary__border-radius: false;
@fc-button-secondary__border-color: false;
@fc-button-secondary__box-shadow: false;
@fc-button-secondary__background: false;
@fc-button-secondary__padding: @fc-button__padding;
@fc-button-secondary__transition: @fc-button__transition;
@fc-button-secondary__hover__color: @fc-button-secondary__color;
@fc-button-secondary__hover__text-shadow: @fc-button-secondary__text-shadow;
@fc-button-secondary__hover__text-decoration: @fc-button-secondary__text-decoration;
@fc-button-secondary__hover__border-width: @fc-button-secondary__border-width;
@fc-button-secondary__hover__border-radius: @fc-button-secondary__border-radius;
@fc-button-secondary__hover__border-color: @fc-button-secondary__border-color;
@fc-button-secondary__hover__box-shadow: @fc-button-secondary__box-shadow;
@fc-button-secondary__hover__background: @fc-button-secondary__background;
@fc-button-secondary__hover__padding: @fc-button-secondary__padding;

@fc-button-primary__color: @fc-button__color;
@fc-button-primary__font-weight: 600;
@fc-button-primary__text-shadow: @fc-button__text-shadow;
@fc-button-primary__text-decoration: @fc-button__text-decoration;
@fc-button-primary__border-width: @fc-button__border-width;
@fc-button-primary__border-radius: @fc-button__border-radius;
@fc-button-primary__border-color: @fc-button__border-color;
@fc-button-primary__box-shadow: @fc-button__box-shadow;
@fc-button-primary__background: @fc-button__background;
@fc-button-primary__padding: @fc-button__padding;
@fc-button-primary__transition: @fc-button__transition;
@fc-button-primary__hover__color: @fc-button-primary__color;
@fc-button-primary__hover__text-shadow: @fc-button-primary__text-shadow;
@fc-button-primary__hover__text-decoration: @fc-button-primary__text-decoration;
@fc-button-primary__hover__border-width: @fc-button-primary__border-width;
@fc-button-primary__hover__border-radius: @fc-button-primary__border-radius;
@fc-button-primary__hover__border-color: @fc-button-primary__border-color;
@fc-button-primary__hover__box-shadow: @fc-button-primary__box-shadow;
@fc-button-primary__hover__background: @fc-button-primary__background;
@fc-button-primary__hover__padding: @fc-button-primary__padding;

@fc-button-order__wide-layout: false;
@fc-button-order__stick-to-bottom: false;
@fc-button-order__color: @fc-button-primary__color;
@fc-button-order__text-shadow: @fc-button-primary__text-shadow;
@fc-button-order__text-decoration: @fc-button-primary__text-decoration;
@fc-button-order__border-width: @fc-button-primary__border-width;
@fc-button-order__border-radius: @fc-button-primary__border-radius;
@fc-button-order__border-color: @fc-button-primary__border-color;
@fc-button-order__box-shadow: @fc-button-primary__box-shadow;
@fc-button-order__background: @fc-button-primary__background;
@fc-button-order__padding: @fc-button-primary__padding;
@fc-button-order__transition: @fc-button-primary__transition;
@fc-button-order__hover__color: @fc-button-order__color;
@fc-button-order__hover__text-shadow: @fc-button-order__text-shadow;
@fc-button-order__hover__text-decoration: @fc-button-order__text-decoration;
@fc-button-order__hover__border-width: @fc-button-order__border-width;
@fc-button-order__hover__border-radius: @fc-button-order__border-radius;
@fc-button-order__hover__border-color: @fc-button-order__border-color;
@fc-button-order__hover__box-shadow: @fc-button-order__box-shadow;
@fc-button-order__hover__background: @fc-button-order__background;
@fc-button-order__hover__padding: @fc-button-order__padding;

@fc-button-auth__enabled: false;
@fc-button-auth__color: @fc-button__color;
@fc-button-auth__text-shadow: @fc-button__text-shadow;
@fc-button-auth__text-decoration: @fc-button__text-decoration;
@fc-button-auth__border-width: @fc-button__border-width;
@fc-button-auth__border-radius: @fc-button__border-radius;
@fc-button-auth__border-color: @fc-button__border-color;
@fc-button-auth__box-shadow: @fc-button__box-shadow;
@fc-button-auth__background: @fc-button__background;
@fc-button-auth__padding: @fc-button__padding;
@fc-button-auth__transition: @fc-button__transition;
@fc-button-auth__hover__color: @fc-button-auth__color;
@fc-button-auth__hover__text-shadow: @fc-button-auth__text-shadow;
@fc-button-auth__hover__text-decoration: @fc-button-auth__text-decoration;
@fc-button-auth__hover__border-width: @fc-button-auth__border-width;
@fc-button-auth__hover__border-radius: @fc-button-auth__border-radius;
@fc-button-auth__hover__border-color: @fc-button-auth__border-color;
@fc-button-auth__hover__box-shadow: @fc-button-auth__box-shadow;
@fc-button-auth__hover__background: @fc-button-auth__background;
@fc-button-auth__hover__padding: @fc-button-auth__padding;

//
// Progress bar
// ______________________________________________
@fc-progress-bar__color: #aaa;
@fc-progress-bar-item__color: #666;
@fc-progress-bar-item__active__color: #333;
@fc-progress-bar-item__completed__color: #006bb4;

//
// Modal popup
// ______________________________________________
@fc-modal__border-radius: false;
@fc-modal__popup-background: @fc-section__background;
@fc-modal__popup-effect: 'slide-up'; //[zoom|slide-down|slide-up]
@fc-modal__popup-box-shadow: 0 0 20px rgba(0, 0, 0, 0.3);
@fc-modal__overlay-background: rgba(0, 0, 0, 0.2);
@fc-modal__divider-background: false;
@fc-modal__divider-color: false;

//
// Intro popup
// ______________________________________________
@fc-intro-popup__overlay-background: rgba(255, 255, 255, 1);

//
// Tooltips (Native Magento's tooltips)
// ______________________________________________
@fc-tooltip-content__box-shadow: 0 0 45px rgba(0, 0, 0, 0.1);
@fc-tooltip-content__background-color: #fff;
@fc-tooltip-content__border-width: 1px;
@fc-tooltip-content__border-color: #fff;
@fc-tooltip-content__width: 200px;

//
// Tiny scrollbar styles
// ______________________________________________
@fc-scrollbar__size: 6px;
@fc-scrollbar__border-radius: 10px;
@fc-scrollbar__background-color: darken(@fc-section__background, 10%);
@fc-scrollbar-thumb__background-color: darken(@fc-scrollbar__background-color, 20%);

//
// Tippy tooltip styles
// ______________________________________________
@fc-tooltip__max-width: 240px;
@fc-tooltip__box-shadow: 0 10px 25px rgba(0, 0, 0, 0.2);
@fc-tooltip__error__background: darken(#e80500, 5%);
@fc-tooltip__warning__background: darken(#dca400, 5%);
@fc-tooltip__content__background: #fff;

//
// Saved addresses variables
// ______________________________________________
@fc-address-item__border-radius: false;
@fc-address-item__border-color: false;
@fc-address-item__hover__background: false;

//
// Shipping methods
// ______________________________________________
@fc-shipping-policy__top: @fc-section__padding + 8px;

//
// Discount
// ______________________________________________
@fc-discount__overlap-button: false;

//
// Order summary
// ______________________________________________
@fc-order-summary__use-overflow-gradient: true;
@fc-order-summary__item-toggler-color: false;

@fc-order-totals__border-color: @fc__divider-color;
@fc-order-totals__border-radius: false;
@fc-order-totals__border-collapse: false;
@fc-order-totals__border: false;
@fc-order-totals__background: none;
@fc-order-totals__padding: false;
@fc-order-totals__margin: 5px 0 10px;

//
// Third-party variables
// ______________________________________________
@fc-orderattachments__border: false;

@fc-delivery-date-icon__margin-left: false;
@fc-delivery-date-icon__color: false;

@fc-checkout-cart__qty-wrapper-width: false;
@fc-checkout-cart__qty-wrapper-border-color: @fc-form-field__border-color;
@fc-checkout-cart__qty-wrapper-border-width: @fc-form-field__border-width;
@fc-checkout-cart__qty-wrapper-border-radius: false;
@fc-checkout-cart__toggler-width: false;
@fc-checkout-cart__toggler-background-color: false;
@fc-checkout-cart__toggler-color: false;
@fc-checkout-cart__toggler-border-color: @fc-checkout-cart__qty-wrapper-border-color;
@fc-checkout-cart__toggler-justify-content: false;

@fc-social-login__link-color: false;
