<li id="email-address" class="checkout-email-address" data-bind="fadeVisible: isVisible">
    <div class="step-title" data-bind="i18n: 'Email'" data-role="title"></div>
    <div id="checkout-step-email" class="step-content" data-role="content">
        <each args="getRegion('customer-email')" render=""></each>
        <each args="getRegion('before-form')" render=""></each>

        <form class="methods-shipping" data-bind="submit: navigateToNextStep" novalidate="novalidate">
            <div class="actions-toolbar">
                <div class="primary">
                    <button data-role="opc-continue" type="submit" class="button action continue primary">
                        <span><!-- ko i18n: 'Next'--><!-- /ko --></span>
                    </button>
                </div>
            </div>
        </form>
    </div>
</li>
