<?php
/** @var  $block \Magento\Framework\View\Element\Template */
/** @var  $viewModel \CopeX\VatValidationFrontend\Helper\Data */

$viewModel = $block->getViewModel();

/** @var \Magento\Framework\View\Helper\SecureHtmlRenderer $secureRenderer*/

?>
<?= $secureRenderer->renderTag('script',[], 'window.VAT_VALIDATION_INPUT_DELAY = ' .  $viewModel->getInputDelay() .'; window.VAT_VALIDATION_URL = \'' .  $block->getUrl($viewModel->getValidationUrl()) .'\';',false); ?>