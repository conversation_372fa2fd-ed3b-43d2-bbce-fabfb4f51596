<?php
/**
 * Template
 * @copyright Copyright © 2024 CopeX GmbH. All rights reserved.
 * <AUTHOR>
 */

declare(strict_types=1);

use Hyva\Theme\Model\ViewModelRegistry;
use Hyva\Theme\ViewModel\StoreConfig;use Magento\Framework\View\Element\Template;


/** @var ViewModelRegistry $viewModels */
/** @var StoreConfig $config */

$config = $viewModels->require(StoreConfig::class);
$iconStyle = $config->getStoreConfig('cms/pagebuilder/accordion_icon_style');
/** @var Template $block */
?>
<script>
    'use strict';
    (() => {
        const initAccordions = () => {
            document
                .querySelectorAll('[data-content-type="accordion"]:not([x-data])')
                .forEach((accordion, accIndex) => {
                    accordion.setAttribute('x-data', 'accordion');
                    accordion.setAttribute('role', 'presentation');
                    accordion.querySelectorAll('[data-content-type="accordion-item"]')
                        .forEach((accordionItem, itemIndex) => {
                            const heading = accordionItem.querySelector('[data-collapsible="true"]');
                            const content = accordionItem.querySelector('[data-content]');

                            const headerId = `accordion-${accIndex}-header-${itemIndex}`;
                            const panelId  = `accordion-${accIndex}-panel-${itemIndex}`;

                            heading.setAttribute('id', headerId);
                            heading.setAttribute('role', 'button');
                            heading.setAttribute('tabindex', '0');
                            heading.setAttribute('aria-controls', panelId);

                            content.setAttribute('id', panelId);
                            content.setAttribute('role', 'region');
                            content.setAttribute('aria-labelledby', headerId);
                            content.setAttribute('data-index', itemIndex);
                            content.setAttribute('x-collapse.duration.300ms', '');

                            if (parseInt(content.dataset.defaultOpened) !== 1) {
                                content.setAttribute('x-cloak', '');
                            }

                            heading.setAttribute(
                                'x-bind:aria-expanded',
                                `openItems.includes(${itemIndex})`
                            );
                            content.setAttribute(
                                'x-show',
                                `openItems.includes(${itemIndex})`
                            );
                            heading.setAttribute(
                                'x-on:click.prevent',
                                `toggle(${itemIndex})`
                            );
                        });
                });
        };

        window.addEventListener('DOMContentLoaded', initAccordions);
        window.addEventListener('alpine:init', () => {
            Alpine.data('accordion', () => ({
                openItems: [],
                allowMultiple: false,

                init() {
                    this.allowMultiple = this.$el.dataset.multiple !== '0';
                    this.openItems = Array.from(
                        this.$el.querySelectorAll('[data-default-opened="1"]')
                    ).map(el => parseInt(el.getAttribute('data-index')));
                },

                toggle(i) {
                    if (this.openItems.includes(i)) {
                        this.openItems = this.openItems.filter(j => j !== i);
                    } else if (this.allowMultiple) {
                        this.openItems = [...this.openItems, i];
                    } else {
                        this.openItems = [i];
                    }
                }
            }));
        });
    })();
</script>
<style>
    div[data-content-type="row"] ul[data-content-type="accordion"], ul[data-content-type="accordion"] { list-style: none; }
    [data-content-type="accordion"] [data-content-type="accordion-item"] { margin: 0; padding: 0;}
    [data-content-type="accordion-item"] > div[data-content] { height: 0px; overflow: hidden;}
    [data-content-type="accordion-item"] > div[data-default-opened="1"]:first-of-type {height: auto;}
    [data-collapsible="true"] { display: flex; }
    [data-content-type="accordion"] [data-collapsible="true"]:after {
        flex-shrink: 0; width: 1.75rem; height: 1.75rem; margin-left: auto; margin-right: 1rem; content: "";
        background-repeat: no-repeat; background-size: 1.75rem; transition: transform .2s ease-in-out;
    <?php if($iconStyle === "plus"): ?>
        background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="w-6 h-6"> <path stroke-linecap="round" stroke-linejoin="round" d="M12 4.5v15m7.5-7.5h-15" /> </svg>');
    <?php else: ?>
        background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="2.5" stroke="currentColor" class="w-6 h-6"> <path stroke-linecap="round" stroke-linejoin="round" d="M19.5 8.25l-7.5 7.5-7.5-7.5" /> </svg>');
    <?php endif; ?>
    }
    <?php if($iconStyle === "plus"): ?>
    [data-content-type="accordion"] [aria-expanded="true"] [data-collapsible="true"]:after { transform: rotate(45deg); }
    <?php else: ?>
    [data-content-type="accordion"] [aria-expanded="true"] [data-collapsible="true"]:after { transform: rotate(-180deg); }
    <?php endif; ?>
</style>