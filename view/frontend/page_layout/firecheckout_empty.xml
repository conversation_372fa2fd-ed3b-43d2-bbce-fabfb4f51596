<?xml version="1.0"?>
<layout xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
    xsi:noNamespaceSchemaLocation="urn:magento:framework:View/Layout/etc/page_layout.xsd">
    <update handle="empty"/>
    <referenceContainer name="page.wrapper">
        <container name="firecheckout.header.container" as="firecheckout_header_container" label="Firecheckout Page Header Container" htmlTag="header" htmlClass="firecheckout-header" before="main.content">
            <container name="firecheckout.header.wrapper" label="Firecheckout Page Header" as="firecheckout_header_wrapper" htmlTag="div" htmlClass="header content"/>
        </container>
    </referenceContainer>
</layout>
