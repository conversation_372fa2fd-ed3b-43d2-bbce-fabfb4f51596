<?xml version="1.0"?>
<page xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
    layout="1column"
    xsi:noNamespaceSchemaLocation="urn:magento:framework:View/Layout/etc/page_configuration.xsd">
    <body>
        <attribute name="class" value="firecheckout-layout-full"/>
        <move element="logo" destination="header-wrapper" before="-"/>

        <!-- <referenceBlock name="header.panel.wrapper" remove="true"/> -->
        <!-- <referenceBlock name="top.search" remove="true"/> -->
        <!-- <referenceBlock name="minicart" remove="true"/> -->
        <!-- <referenceBlock name="footer-container" remove="true"/> -->
        <!-- <referenceBlock name="copyright" remove="true"/> -->
    </body>
</page>
