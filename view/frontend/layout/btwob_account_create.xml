<?xml version="1.0"?>
<page xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="urn:magento:framework:View/Layout/etc/page_configuration.xsd">
    <head>
        <script src="CopeX_VatValidationFrontend::js/vat_validation_frontend.js"/>
        <css src="CopeX_VatValidationFrontend::css/custom.css"/>
    </head>
    <body>
        <referenceContainer name="before.body.end">
            <block name="copex.vat_validation.checkout" template="CopeX_VatValidationFrontend::enabled.phtml" ifconfig="copex_vat_validator/vat_validation_frontend/enable_registration">
                <arguments>
                    <argument name="view_model" xsi:type="object">CopeX\VatValidationFrontend\Helper\Data</argument>
                </arguments>
            </block>
        </referenceContainer>
    </body>
</page>
