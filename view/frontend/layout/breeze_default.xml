<?xml version="1.0"?>
<page xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:noNamespaceSchemaLocation="urn:magento:framework:View/Layout/etc/page_configuration.xsd">
    <body>
        <referenceBlock name="breeze.js">
            <arguments>
                <argument name="bundles" xsi:type="array">
                    <item name="default" xsi:type="array">
                        <item name="items" xsi:type="array">
                            <item name="Swissup_Firecheckout/js/prefetch" xsi:type="array">
                                <item name="path" xsi:type="string">Swissup_Firecheckout/js/prefetch</item>
                                <item name="enabled" xsi:type="helper" helper="Swissup\Breeze\Helper\Config::isAllEnabled">
                                    <param name="enabled">firecheckout/general/enabled</param>
                                    <param name="jsbuild">firecheckout/performance/jsbuild</param>
                                    <param name="prefetch">firecheckout/performance/prefetch</param>
                                </item>
                            </item>
                        </item>
                    </item>
                </argument>
            </arguments>
        </referenceBlock>
    </body>
</page>
