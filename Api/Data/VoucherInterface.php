<?php
/**
 * Copyright ©  All rights reserved.
 * See COPYING.txt for license details.
 */
declare(strict_types=1);

namespace Incert\Voucher\Api\Data;

interface VoucherInterface
{

    const AMOUNT = 'amount';
    const REDEMPTION_AMOUNT = 'redemption_amount';
    const CODE = 'code';
    const INVOICE_ID = 'invoice_id';
    const ORDER_ID = 'order_id';
    const QUOTE_ID = 'quote_id';
    const CURRENCY = 'currency';
    const REDEMPTION_ID = 'redemption_id';
    const VOUCHER_INFO = 'voucher_info';
    const CREDITMEMO_ID = 'creditmemo_id';
    const VOUCHER_ID = 'voucher_id';

    /**
     * Get voucher_id
     * @return string|null
     */
    public function getVoucherId();

    /**
     * Set voucher_id
     * @param string $voucherId
     * @return \Incert\Voucher\Voucher\Api\Data\VoucherInterface
     */
    public function setVoucherId($voucherId);

    /**
     * Get quote_id
     * @return string|null
     */
    public function getQuoteId();

    /**
     * Set quote_id
     * @param string $quoteId
     * @return \Incert\Voucher\Voucher\Api\Data\VoucherInterface
     */
    public function setQuoteId($quoteId);

    /**
     * Get order_id
     * @return string|null
     */
    public function getOrderId();

    /**
     * Set order_id
     * @param string $orderId
     * @return \Incert\Voucher\Voucher\Api\Data\VoucherInterface
     */
    public function setOrderId($orderId);

    /**
     * Get invoice_id
     * @return string|null
     */
    public function getInvoiceId();

    /**
     * Set invoice_id
     * @param string $invoiceId
     * @return \Incert\Voucher\Voucher\Api\Data\VoucherInterface
     */
    public function setInvoiceId($invoiceId);

    /**
     * Get creditmemo_id
     * @return string|null
     */
    public function getCreditmemoId();

    /**
     * Set creditmemo_id
     * @param string $creditmemoId
     * @return \Incert\Voucher\Voucher\Api\Data\VoucherInterface
     */
    public function setCreditmemoId($creditmemoId);

    /**
     * Get amount
     * @return string|null
     */
    public function getAmount();

    /**
     * Set amount
     * @param string $amount
     * @return \Incert\Voucher\Voucher\Api\Data\VoucherInterface
     */
    public function setAmount($amount);

    /**
     * Get currency
     * @return string|null
     */
    public function getCurrency();

    /**
     * Set currency
     * @param string $currency
     * @return \Incert\Voucher\Voucher\Api\Data\VoucherInterface
     */
    public function setCurrency($currency);

    /**
     * Get redemption_id
     * @return string|null
     */
    public function getRedemptionId();

    /**
     * Set redemption_id
     * @param string $redemptionId
     * @return \Incert\Voucher\Voucher\Api\Data\VoucherInterface
     */
    public function setRedemptionId($redemptionId);

    /**
     * Get redemption_amount
     * @return string|null
     */
    public function getRedemptionAmount();

    /**
     * Set redemption_amount
     * @param string $redemptionAmount
     * @return \Incert\Voucher\Voucher\Api\Data\VoucherInterface
     */
    public function setRedemptionAmount($redemptionAmount);

    /**
     * Get voucher_info
     * @return string|null
     */
    public function getVoucherInfo();

    /**
     * Set voucher_info
     * @param string $voucherInfo
     * @return \Incert\Voucher\Voucher\Api\Data\VoucherInterface
     */
    public function setVoucherInfo($voucherInfo);

    /**
     * Get code
     * @return string|null
     */
    public function getCode();

    /**
     * Set code
     * @param string $code
     * @return \Incert\Voucher\Voucher\Api\Data\VoucherInterface
     */
    public function setCode($code);
}
