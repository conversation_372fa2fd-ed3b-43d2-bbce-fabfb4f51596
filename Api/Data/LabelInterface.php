<?php


namespace CopeX\Plc\Api\Data;

interface LabelInterface extends \Magento\Framework\Api\ExtensibleDataInterface
{

    public const POST_CODE_NUMBER = 'post_code_number';
    public const LABEL_ID = 'label_id';
    public const UPDATED_AT = 'updated_at';
    public const LABEL_PATH = 'label_path';
    public const LABEL_NAME = 'label_name';
    public const ORDER_ID = 'order_id';
    public const CREATED_AT = 'created_at';
    public const PACKET_OPTION = 'packet_option';
    public const CUSTOMER_NAME = 'customer_name';
    public const SHIPPING_NAME = 'shipping_name';
    public const SHIPPING_ADDRESS = 'shipping_address';

    /**
     * Get label_id
     * @return string|null
     */
    public function getLabelId();

    /**
     * Get customer_name
     * @return string|null
     */
    public function getCustomerName();

    /**
     * Get shipping_name
     * @return string|null
     */
    public function getShippingName();

    /**
     * Get shipping_address
     * @return string|null
     */
    public function getShippingAddress();

    /**
     * Set label_id
     * @param string $labelId
     * @return \CopeX\Plc\Api\Data\LabelInterface
     */
    public function setLabelId($labelId);

    /**
     * Get label_name
     * @return string|null
     */
    public function getLabelName();

    /**
     * Set label_name
     * @param string $labelName
     * @return \CopeX\Plc\Api\Data\LabelInterface
     */
    public function setLabelName($labelName);

    /**
     * Get label_path
     * @return string|null
     */
    public function getLabelPath();

    /**
     * Set label_path
     * @param string $labelPath
     * @return \CopeX\Plc\Api\Data\LabelInterface
     */
    public function setLabelPath($labelPath);

    /**
     * Get order_id
     * @return string|null
     */
    public function getOrderId();

    /**
     * Set order_id
     * @param string $orderId
     * @return \CopeX\Plc\Api\Data\LabelInterface
     */
    public function setOrderId($orderId);

    /**
     * Get post_code_number
     * @return string|null
     */
    public function getPostCodeNumber();

    /**
     * Set post_code_number
     * @param string $postCodeNumber
     * @return \CopeX\Plc\Api\Data\LabelInterface
     */
    public function setPostCodeNumber($postCodeNumber);

    /**
     * Get created_at
     * @return string|null
     */
    public function getCreatedAt();

    /**
     * Set created_at
     * @param string $createdAt
     * @return \CopeX\Plc\Api\Data\LabelInterface
     */
    public function setCreatedAt($createdAt);

    /**
     * Get updated_at
     * @return string|null
     */
    public function getUpdatedAt();

    /**
     * Set updated_at
     * @param string $updatedAt
     * @return \CopeX\Plc\Api\Data\LabelInterface
     */
    public function setUpdatedAt($updatedAt);

    /**
     * Get packet_option
     * @return string|null
     */
    public function getPacketOption();

    /**
     * Set packet_option
     * @param string $packetOption
     * @return \CopeX\Plc\Api\Data\LabelInterface
     */
    public function setPacketOption($packetOption);
}
